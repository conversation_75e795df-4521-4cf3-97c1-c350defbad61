// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.data.repository;

import android.content.Context;
import android.speech.tts.TextToSpeech;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VoiceRepositoryImpl_Factory implements Factory<VoiceRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<TextToSpeech> textToSpeechProvider;

  public VoiceRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<TextToSpeech> textToSpeechProvider) {
    this.contextProvider = contextProvider;
    this.textToSpeechProvider = textToSpeechProvider;
  }

  @Override
  public VoiceRepositoryImpl get() {
    return newInstance(contextProvider.get(), textToSpeechProvider.get());
  }

  public static VoiceRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<TextToSpeech> textToSpeechProvider) {
    return new VoiceRepositoryImpl_Factory(contextProvider, textToSpeechProvider);
  }

  public static VoiceRepositoryImpl newInstance(Context context, TextToSpeech textToSpeech) {
    return new VoiceRepositoryImpl(context, textToSpeech);
  }
}
