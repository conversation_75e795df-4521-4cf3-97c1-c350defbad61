package com.zara.assistant.domain.model;

/**
 * Learning configuration
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u001d\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003JY\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010!\u001a\u00020\u00032\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020\u0005H\u00d6\u0001J\t\u0010$\u001a\u00020%H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000f\u00a8\u0006&"}, d2 = {"Lcom/zara/assistant/domain/model/LearningConfig;", "", "isEnabled", "", "minInteractionsForPattern", "", "minConfidenceForSuggestion", "", "maxSuggestionsPerDay", "dataRetentionDays", "enableWebSearch", "enableMLTraining", "enableProactiveMode", "(ZIFIIZZZ)V", "getDataRetentionDays", "()I", "getEnableMLTraining", "()Z", "getEnableProactiveMode", "getEnableWebSearch", "getMaxSuggestionsPerDay", "getMinConfidenceForSuggestion", "()F", "getMinInteractionsForPattern", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
public final class LearningConfig {
    private final boolean isEnabled = false;
    private final int minInteractionsForPattern = 0;
    private final float minConfidenceForSuggestion = 0.0F;
    private final int maxSuggestionsPerDay = 0;
    private final int dataRetentionDays = 0;
    private final boolean enableWebSearch = false;
    private final boolean enableMLTraining = false;
    private final boolean enableProactiveMode = false;
    
    public LearningConfig(boolean isEnabled, int minInteractionsForPattern, float minConfidenceForSuggestion, int maxSuggestionsPerDay, int dataRetentionDays, boolean enableWebSearch, boolean enableMLTraining, boolean enableProactiveMode) {
        super();
    }
    
    public final boolean isEnabled() {
        return false;
    }
    
    public final int getMinInteractionsForPattern() {
        return 0;
    }
    
    public final float getMinConfidenceForSuggestion() {
        return 0.0F;
    }
    
    public final int getMaxSuggestionsPerDay() {
        return 0;
    }
    
    public final int getDataRetentionDays() {
        return 0;
    }
    
    public final boolean getEnableWebSearch() {
        return false;
    }
    
    public final boolean getEnableMLTraining() {
        return false;
    }
    
    public final boolean getEnableProactiveMode() {
        return false;
    }
    
    public LearningConfig() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.LearningConfig copy(boolean isEnabled, int minInteractionsForPattern, float minConfidenceForSuggestion, int maxSuggestionsPerDay, int dataRetentionDays, boolean enableWebSearch, boolean enableMLTraining, boolean enableProactiveMode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}