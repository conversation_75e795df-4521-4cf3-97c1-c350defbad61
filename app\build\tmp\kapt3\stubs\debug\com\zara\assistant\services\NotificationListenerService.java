package com.zara.assistant.services;

/**
 * Service for listening to and managing notifications
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u0000 02\u00020\u0001:\u00010B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0005H\u0002J\u0006\u0010\u0015\u001a\u00020\tJ\u000e\u0010\u0016\u001a\u00020\t2\u0006\u0010\u0017\u001a\u00020\u0018J\u0012\u0010\u0019\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0016\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u00072\u0006\u0010\u0014\u001a\u00020\u001eH\u0002J\u0010\u0010\u001f\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\u0018H\u0002J\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007J\u0010\u0010\"\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u001eH\u0002J\u0010\u0010#\u001a\u00020\t2\u0006\u0010$\u001a\u00020%H\u0002J\b\u0010&\u001a\u00020\u0013H\u0002J\b\u0010\'\u001a\u00020\u0013H\u0016J\b\u0010(\u001a\u00020\u0013H\u0016J\b\u0010)\u001a\u00020\u0013H\u0016J\u0012\u0010*\u001a\u00020\u00132\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u0016J\u0012\u0010+\u001a\u00020\u00132\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u0016J\u0010\u0010,\u001a\u00020\u00132\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0016\u0010-\u001a\u00020\t2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010.\u001a\u00020\u0018J\u0010\u0010/\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u0005H\u0002R\u0016\u0010\u0003\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0019\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/zara/assistant/services/NotificationListenerService;", "Landroid/service/notification/NotificationListenerService;", "()V", "_newNotification", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/services/ZaraNotification;", "_notifications", "", "isServiceReady", "", "newNotification", "Lkotlinx/coroutines/flow/StateFlow;", "getNewNotification", "()Lkotlinx/coroutines/flow/StateFlow;", "notifications", "getNotifications", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "addNotification", "", "notification", "clearAllNotifications", "clearNotification", "key", "", "convertToZaraNotification", "sbn", "Landroid/service/notification/StatusBarNotification;", "extractNotificationActions", "Lcom/zara/assistant/services/NotificationAction;", "Landroid/app/Notification;", "getAppName", "packageName", "getNotificationsForReading", "hasReplyAction", "isReplyAction", "action", "Landroid/app/Notification$Action;", "loadActiveNotifications", "onDestroy", "onListenerConnected", "onListenerDisconnected", "onNotificationPosted", "onNotificationRemoved", "removeNotification", "replyToNotification", "replyText", "shouldProcessNotification", "Companion", "app_debug"})
public final class NotificationListenerService extends android.service.notification.NotificationListenerService {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "NotificationListener";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.zara.assistant.services.ZaraNotification>> _notifications = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.zara.assistant.services.ZaraNotification>> notifications = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.services.ZaraNotification> _newNotification = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.services.ZaraNotification> newNotification = null;
    private boolean isServiceReady = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.NotificationListenerService.Companion Companion = null;
    
    public NotificationListenerService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.zara.assistant.services.ZaraNotification>> getNotifications() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.services.ZaraNotification> getNewNotification() {
        return null;
    }
    
    @java.lang.Override()
    public void onListenerConnected() {
    }
    
    @java.lang.Override()
    public void onListenerDisconnected() {
    }
    
    @java.lang.Override()
    public void onNotificationPosted(@org.jetbrains.annotations.Nullable()
    android.service.notification.StatusBarNotification sbn) {
    }
    
    @java.lang.Override()
    public void onNotificationRemoved(@org.jetbrains.annotations.Nullable()
    android.service.notification.StatusBarNotification sbn) {
    }
    
    private final void loadActiveNotifications() {
    }
    
    private final com.zara.assistant.services.ZaraNotification convertToZaraNotification(android.service.notification.StatusBarNotification sbn) {
        return null;
    }
    
    private final java.lang.String getAppName(java.lang.String packageName) {
        return null;
    }
    
    private final java.util.List<com.zara.assistant.services.NotificationAction> extractNotificationActions(android.app.Notification notification) {
        return null;
    }
    
    private final boolean hasReplyAction(android.app.Notification notification) {
        return false;
    }
    
    private final boolean isReplyAction(android.app.Notification.Action action) {
        return false;
    }
    
    private final boolean shouldProcessNotification(com.zara.assistant.services.ZaraNotification notification) {
        return false;
    }
    
    private final void addNotification(com.zara.assistant.services.ZaraNotification notification) {
    }
    
    private final void removeNotification(java.lang.String key) {
    }
    
    /**
     * Get notifications for reading aloud
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.zara.assistant.services.ZaraNotification> getNotificationsForReading() {
        return null;
    }
    
    /**
     * Clear a specific notification
     */
    public final boolean clearNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return false;
    }
    
    /**
     * Clear all clearable notifications
     */
    public final boolean clearAllNotifications() {
        return false;
    }
    
    /**
     * Reply to a notification
     */
    public final boolean replyToNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    java.lang.String replyText) {
        return false;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/zara/assistant/services/NotificationListenerService$Companion;", "", "()V", "TAG", "", "isEnabled", "", "context", "Landroid/content/Context;", "requestPermission", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final boolean isEnabled(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return false;
        }
        
        public final void requestPermission(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
    }
}