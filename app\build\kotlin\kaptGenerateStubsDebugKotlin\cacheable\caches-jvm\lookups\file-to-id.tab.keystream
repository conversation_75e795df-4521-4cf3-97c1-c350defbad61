7app/src/main/java/com/zara/assistant/ZaraApplication.kt6app/src/main/java/com/zara/assistant/core/Constants.ktAapp/src/main/java/com/zara/assistant/data/local/dao/CommandDao.ktFapp/src/main/java/com/zara/assistant/data/local/dao/ConversationDao.ktBapp/src/main/java/com/zara/assistant/data/local/dao/SettingsDao.ktFapp/src/main/java/com/zara/assistant/data/local/dao/UserLearningDao.ktFapp/src/main/java/com/zara/assistant/data/local/database/Converters.ktHapp/src/main/java/com/zara/assistant/data/local/database/ZaraDatabase.ktWapp/src/main/java/com/zara/assistant/data/local/database/entities/ConversationEntity.ktSapp/src/main/java/com/zara/assistant/data/local/database/entities/SettingsEntity.ktQapp/src/main/java/com/zara/assistant/data/local/preferences/PreferencesManager.ktHapp/src/main/java/com/zara/assistant/data/remote/api/CohereApiService.ktLapp/src/main/java/com/zara/assistant/data/remote/api/PerplexityApiService.ktEapp/src/main/java/com/zara/assistant/data/remote/dto/CohereRequest.ktFapp/src/main/java/com/zara/assistant/data/remote/dto/CohereResponse.ktIapp/src/main/java/com/zara/assistant/data/remote/dto/PerplexityRequest.ktJapp/src/main/java/com/zara/assistant/data/remote/dto/PerplexityResponse.ktOapp/src/main/java/com/zara/assistant/data/remote/interceptor/AuthInterceptor.ktHapp/src/main/java/com/zara/assistant/data/repository/AIRepositoryImpl.ktRapp/src/main/java/com/zara/assistant/data/repository/ConversationRepositoryImpl.ktNapp/src/main/java/com/zara/assistant/data/repository/SettingsRepositoryImpl.ktKapp/src/main/java/com/zara/assistant/data/repository/VoiceRepositoryImpl.kt4app/src/main/java/com/zara/assistant/di/AppModule.kt9app/src/main/java/com/zara/assistant/di/DatabaseModule.kt8app/src/main/java/com/zara/assistant/di/NetworkModule.kt8app/src/main/java/com/zara/assistant/di/ServiceModule.kt?app/src/main/java/com/zara/assistant/domain/model/AIResponse.ktAapp/src/main/java/com/zara/assistant/domain/model/Conversation.ktGapp/src/main/java/com/zara/assistant/domain/model/ConversationModels.ktAapp/src/main/java/com/zara/assistant/domain/model/SystemAction.ktGapp/src/main/java/com/zara/assistant/domain/model/UserLearningModels.ktAapp/src/main/java/com/zara/assistant/domain/model/VoiceCommand.kt?app/src/main/java/com/zara/assistant/domain/model/VoiceState.ktFapp/src/main/java/com/zara/assistant/domain/repository/AIRepository.ktPapp/src/main/java/com/zara/assistant/domain/repository/ConversationRepository.ktLapp/src/main/java/com/zara/assistant/domain/repository/SettingsRepository.ktIapp/src/main/java/com/zara/assistant/domain/repository/VoiceRepository.ktQapp/src/main/java/com/zara/assistant/domain/usecase/ProcessVoiceCommandUseCase.ktQapp/src/main/java/com/zara/assistant/presentation/components/NeumorphismButton.ktOapp/src/main/java/com/zara/assistant/presentation/components/NeumorphismCard.ktKapp/src/main/java/com/zara/assistant/presentation/components/VoiceButton.ktRapp/src/main/java/com/zara/assistant/presentation/components/VoiceVisualization.kt@app/src/main/java/com/zara/assistant/presentation/theme/Color.kt@app/src/main/java/com/zara/assistant/presentation/theme/Shape.kt@app/src/main/java/com/zara/assistant/presentation/theme/Theme.kt?app/src/main/java/com/zara/assistant/presentation/theme/Type.ktKapp/src/main/java/com/zara/assistant/presentation/utils/PerformanceUtils.ktGapp/src/main/java/com/zara/assistant/services/AIOrchestrationService.ktDapp/src/main/java/com/zara/assistant/services/AIProcessingService.ktOapp/src/main/java/com/zara/assistant/services/AdvancedVoiceProcessingService.ktDapp/src/main/java/com/zara/assistant/services/AzureOnlySTTService.ktDapp/src/main/java/com/zara/assistant/services/ConversationManager.ktFapp/src/main/java/com/zara/assistant/services/LocalCommandProcessor.ktIapp/src/main/java/com/zara/assistant/services/MLPersonalizationService.ktLapp/src/main/java/com/zara/assistant/services/NotificationListenerService.ktFapp/src/main/java/com/zara/assistant/services/PersonalMemoryService.ktEapp/src/main/java/com/zara/assistant/services/SystemControlManager.ktDapp/src/main/java/com/zara/assistant/services/UserLearningService.kt@app/src/main/java/com/zara/assistant/services/WakeWordService.ktAapp/src/main/java/com/zara/assistant/services/WebSearchService.ktIapp/src/main/java/com/zara/assistant/services/ZaraAccessibilityService.ktHapp/src/main/java/com/zara/assistant/services/ZaraDeviceAdminReceiver.kt7app/src/main/java/com/zara/assistant/ui/MainActivity.ktJapp/src/main/java/com/zara/assistant/ui/RestrictedSettingsGuideActivity.ktDapp/src/main/java/com/zara/assistant/ui/navigation/ZaraNavigation.kt>app/src/main/java/com/zara/assistant/ui/screens/AboutScreen.ktEapp/src/main/java/com/zara/assistant/ui/screens/CommandsHelpScreen.kt=app/src/main/java/com/zara/assistant/ui/screens/MainScreen.ktCapp/src/main/java/com/zara/assistant/ui/screens/OnboardingScreen.ktDapp/src/main/java/com/zara/assistant/ui/screens/PermissionsScreen.ktAapp/src/main/java/com/zara/assistant/ui/screens/SettingsScreen.ktBapp/src/main/java/com/zara/assistant/ui/viewmodel/MainViewModel.ktFapp/src/main/java/com/zara/assistant/ui/viewmodel/SettingsViewModel.kt;app/src/main/java/com/zara/assistant/utils/ApiKeyManager.kt>app/src/main/java/com/zara/assistant/utils/PerformanceUtils.kt?app/src/main/java/com/zara/assistant/utils/ResponseOptimizer.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 