1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.zara.assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Audio and Speech Permissions -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:22-77
14
15    <!-- Network Permissions -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:5-67
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- System Control Permissions -->
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:5-78
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:22-75
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:5-68
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:22-65
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:5-77
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
23-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:17:5-88
23-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:17:22-85
24
25    <!-- WiFi Control Permissions -->
26    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:5-76
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:22-73
27    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
27-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:21:5-76
27-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:21:22-73
28
29    <!-- Bluetooth Control Permissions -->
30    <uses-permission android:name="android.permission.BLUETOOTH" />
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:5-68
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:22-65
31    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:5-74
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:22-71
32    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:5-76
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:22-73
33    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:5-73
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:22-70
34    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
34-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:28:5-78
34-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:28:22-75
35    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
35-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:29:5-30:47
35-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:29:22-76
36
37    <!-- Mobile Data and Network Control -->
38    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:5-79
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:22-76
39    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:5-35:47
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:22-77
40
41    <!-- Hotspot Control -->
42    <uses-permission android:name="android.permission.TETHER_PRIVILEGED" />
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:38:5-39:47
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:38:22-73
43
44    <!-- NFC Control -->
45    <uses-permission android:name="android.permission.NFC" />
45-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:5-62
45-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:22-59
46    <uses-permission android:name="android.permission.NFC_PREFERRED_PAYMENT_INFO" />
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:5-85
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:22-82
47
48    <!-- Location Services Control -->
49    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:5-79
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:22-76
50    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
50-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:5-81
50-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:22-78
51    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
51-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:5-85
51-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:22-82
52
53    <!-- Device Control Permissions -->
54    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:5-76
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:22-73
55    <uses-permission android:name="android.permission.REORDER_TASKS" />
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:52:5-72
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:52:22-69
56    <uses-permission android:name="android.permission.GET_TASKS" />
56-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:53:5-68
56-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:53:22-65
57    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
57-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:5-84
57-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:22-81
58    <uses-permission android:name="android.permission.SET_WALLPAPER" />
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:5-72
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:22-69
59    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:56:5-78
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:56:22-75
60    <uses-permission android:name="android.permission.DEVICE_POWER" />
60-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:5-58:47
60-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:22-68
61    <uses-permission android:name="android.permission.REBOOT" />
61-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:5-60:47
61-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:22-62
62
63    <!-- Screen and Display Control -->
64    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
64-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:5-79
64-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:22-76
65    <uses-permission android:name="android.permission.FLASHLIGHT" />
65-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:5-69
65-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:22-66
66
67    <!-- Telephony Control -->
68    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
68-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:5-75
68-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:22-72
69    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
69-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:5-77
69-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:22-74
70    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
70-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:5-70:47
70-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:22-74
71
72    <!-- App Management Permissions -->
73    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
73-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:73:5-77
73-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:73:22-74
74    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
74-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-75:47
74-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:22-75
75
76    <!-- System Settings Access -->
77    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
77-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:5-73
77-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:22-70
78    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
78-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:5-80:47
78-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:22-77
79    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
79-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:5-85
79-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:22-82
80
81    <!-- Notification Permissions -->
82    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
82-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:5-93
82-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:22-90
83    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
83-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:5-77
83-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:22-74
84
85    <!-- Accessibility Permission -->
86    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
86-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:88:5-85
86-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:88:22-82
87
88    <!-- Phone and Device Control -->
89    <uses-permission android:name="android.permission.CALL_PHONE" />
89-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:5-69
89-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:22-66
90    <uses-permission android:name="android.permission.SEND_SMS" />
90-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:5-67
90-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:22-64
91    <uses-permission android:name="android.permission.READ_CONTACTS" />
91-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:5-72
91-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:22-69
92
93    <!-- Storage Permissions -->
94    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
94-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:5-80
94-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:22-77
95    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
95-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:5-81
95-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:22-78
96
97    <!-- Advanced System Control -->
98    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
98-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:5-101:47
98-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:22-77
99    <uses-permission android:name="android.permission.MANAGE_USERS" />
99-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:102:5-103:47
99-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:102:22-68
100    <uses-permission android:name="android.permission.STATUS_BAR" />
100-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:104:5-105:47
100-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:104:22-66
101
102    <!-- Root-level System Access (for advanced features) -->
103    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
103-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:5-109:47
103-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:22-72
104    <uses-permission android:name="android.permission.HARDWARE_TEST" />
104-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:5-111:47
104-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:22-69
105
106    <!-- Device Admin Permissions -->
107    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
107-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:5-76
107-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:22-73
108    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
109
110    <permission
110-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
111        android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
111-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
112        android:protectionLevel="signature" />
112-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
113
114    <uses-permission android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
114-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
114-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
115
116    <application
116-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:116:5-228:19
117        android:name="com.zara.assistant.ZaraApplication"
117-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:117:9-40
118        android:allowBackup="true"
118-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:118:9-35
119        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
119-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
120        android:dataExtractionRules="@xml/data_extraction_rules"
120-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:119:9-65
121        android:extractNativeLibs="false"
122        android:fullBackupContent="@xml/backup_rules"
122-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:9-54
123        android:icon="@mipmap/ic_launcher"
123-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:121:9-43
124        android:label="@string/app_name"
124-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:122:9-41
125        android:roundIcon="@mipmap/ic_launcher_round"
125-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:123:9-54
126        android:supportsRtl="true"
126-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:124:9-35
127        android:theme="@style/Theme.Zara"
127-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:9-42
128        android:usesCleartextTraffic="false" >
128-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:126:9-45
129
130        <!-- Main Activity -->
131        <activity
131-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:130:9-144:20
132            android:name="com.zara.assistant.ui.MainActivity"
132-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:131:13-44
133            android:exported="true"
133-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:132:13-36
134            android:launchMode="singleTop"
134-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:135:13-43
135            android:screenOrientation="portrait"
135-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:134:13-49
136            android:theme="@style/Theme.Zara" >
136-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:133:13-46
137            <intent-filter>
137-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:136:13-139:29
138                <action android:name="android.intent.action.MAIN" />
138-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:17-69
138-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:25-66
139
140                <category android:name="android.intent.category.LAUNCHER" />
140-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:17-77
140-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:27-74
141            </intent-filter>
142            <intent-filter>
142-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:140:13-143:29
143                <action android:name="android.intent.action.VOICE_COMMAND" />
143-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:141:17-78
143-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:141:25-75
144
145                <category android:name="android.intent.category.DEFAULT" />
145-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:142:17-76
145-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:142:27-73
146            </intent-filter>
147        </activity>
148
149        <!-- Restricted Settings Guide Activity -->
150        <activity
150-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:147:9-151:52
151            android:name="com.zara.assistant.ui.RestrictedSettingsGuideActivity"
151-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:148:13-63
152            android:exported="false"
152-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:149:13-37
153            android:screenOrientation="portrait"
153-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:13-49
154            android:theme="@style/Theme.Zara" />
154-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:150:13-46
155
156        <!-- Wake Word Detection Service -->
157        <service
157-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:154:9-158:58
158            android:name="com.zara.assistant.services.WakeWordService"
158-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:155:13-53
159            android:enabled="true"
159-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:156:13-35
160            android:exported="false"
160-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:157:13-37
161            android:foregroundServiceType="microphone" />
161-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:158:13-55
162
163        <!-- Advanced Voice Processing Service -->
164        <service
164-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:161:9-165:58
165            android:name="com.zara.assistant.services.AdvancedVoiceProcessingService"
165-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:162:13-68
166            android:enabled="true"
166-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:163:13-35
167            android:exported="false"
167-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:164:13-37
168            android:foregroundServiceType="microphone" />
168-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:165:13-55
169
170        <!-- Accessibility Service for System Control -->
171        <service
171-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:168:9-178:19
172            android:name="com.zara.assistant.services.ZaraAccessibilityService"
172-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:169:13-62
173            android:exported="true"
173-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:171:13-36
174            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
174-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:170:13-79
175            <intent-filter>
175-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:172:13-174:29
176                <action android:name="android.accessibilityservice.AccessibilityService" />
176-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:173:17-92
176-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:173:25-89
177            </intent-filter>
178
179            <meta-data
179-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:175:13-177:72
180                android:name="android.accessibilityservice"
180-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:176:17-60
181                android:resource="@xml/accessibility_service_config" />
181-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:177:17-69
182        </service>
183
184        <!-- Device Admin Receiver for Enhanced System Control -->
185        <receiver
185-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:181:9-192:20
186            android:name="com.zara.assistant.services.ZaraDeviceAdminReceiver"
186-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:182:13-61
187            android:exported="true"
187-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:184:13-36
188            android:permission="android.permission.BIND_DEVICE_ADMIN" >
188-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:183:13-70
189            <meta-data
189-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:185:13-187:63
190                android:name="android.app.device_admin"
190-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:186:17-56
191                android:resource="@xml/device_admin_config" />
191-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:187:17-60
192
193            <intent-filter>
193-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:188:13-191:29
194                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
194-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:189:17-82
194-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:189:25-79
195                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
195-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:190:17-83
195-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:190:25-80
196            </intent-filter>
197        </receiver>
198
199        <!-- Notification Listener Service -->
200        <service
200-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:195:9-202:19
201            android:name="com.zara.assistant.services.NotificationListenerService"
201-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:196:13-65
202            android:exported="true"
202-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:198:13-36
203            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
203-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:197:13-87
204            <intent-filter>
204-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:199:13-201:29
205                <action android:name="android.service.notification.NotificationListenerService" />
205-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:200:17-99
205-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:200:25-96
206            </intent-filter>
207        </service>
208
209        <!-- AI Processing Service -->
210        <service
210-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:205:9-208:40
211            android:name="com.zara.assistant.services.AIProcessingService"
211-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:206:13-57
212            android:enabled="true"
212-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:207:13-35
213            android:exported="false" />
213-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:208:13-37
214
215        <!-- User Learning Service -->
216        <service
216-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:211:9-214:40
217            android:name="com.zara.assistant.services.UserLearningService"
217-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:212:13-57
218            android:enabled="true"
218-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:213:13-35
219            android:exported="false" />
219-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:214:13-37
220
221        <!-- Work Manager for Background Tasks -->
222        <provider
223            android:name="androidx.startup.InitializationProvider"
223-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:218:13-67
224            android:authorities="com.zara.assistant.androidx-startup"
224-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:219:13-68
225            android:exported="false" >
225-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:220:13-37
226            <meta-data
226-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
227                android:name="androidx.emoji2.text.EmojiCompatInitializer"
227-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
228                android:value="androidx.startup" />
228-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
229            <meta-data
229-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
230                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
230-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
231                android:value="androidx.startup" />
231-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
232            <meta-data
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
234                android:value="androidx.startup" />
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
235        </provider>
236
237        <service
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
238            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
240            android:enabled="@bool/enable_system_alarm_service_default"
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
241            android:exported="false" />
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
242        <service
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
243            android:name="androidx.work.impl.background.systemjob.SystemJobService"
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
244            android:directBootAware="false"
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
245            android:enabled="@bool/enable_system_job_service_default"
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
246            android:exported="true"
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
247            android:permission="android.permission.BIND_JOB_SERVICE" />
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
248        <service
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
249            android:name="androidx.work.impl.foreground.SystemForegroundService"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
251            android:enabled="@bool/enable_system_foreground_service_default"
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
252            android:exported="false" />
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
253
254        <receiver
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
255            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
256            android:directBootAware="false"
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
257            android:enabled="true"
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
258            android:exported="false" />
258-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
259        <receiver
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
262            android:enabled="false"
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
265                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
266                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
267            </intent-filter>
268        </receiver>
269        <receiver
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
275                <action android:name="android.intent.action.BATTERY_OKAY" />
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
276                <action android:name="android.intent.action.BATTERY_LOW" />
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
277            </intent-filter>
278        </receiver>
279        <receiver
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
280            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
285                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
286                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
295                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
296            </intent-filter>
297        </receiver>
298        <receiver
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
299            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
300            android:directBootAware="false"
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
301            android:enabled="false"
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
302            android:exported="false" >
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
303            <intent-filter>
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
304                <action android:name="android.intent.action.BOOT_COMPLETED" />
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
305                <action android:name="android.intent.action.TIME_SET" />
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
306                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
310            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
311            android:directBootAware="false"
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
312            android:enabled="@bool/enable_system_alarm_service_default"
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
313            android:exported="false" >
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
314            <intent-filter>
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
315                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
316            </intent-filter>
317        </receiver>
318        <receiver
318-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
319            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
320            android:directBootAware="false"
320-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
321            android:enabled="true"
321-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
322            android:exported="true"
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
323            android:permission="android.permission.DUMP" >
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
324            <intent-filter>
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
325                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
326            </intent-filter>
327        </receiver>
328
329        <service
329-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
330            android:name="androidx.room.MultiInstanceInvalidationService"
330-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
331            android:directBootAware="true"
331-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
332            android:exported="false" />
332-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
333
334        <receiver
334-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
335            android:name="androidx.profileinstaller.ProfileInstallReceiver"
335-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
336            android:directBootAware="false"
336-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
337            android:enabled="true"
337-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
338            android:exported="true"
338-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
339            android:permission="android.permission.DUMP" >
339-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
340            <intent-filter>
340-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
341                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
341-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
341-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
342            </intent-filter>
343            <intent-filter>
343-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
344                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
344-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
344-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
345            </intent-filter>
346            <intent-filter>
346-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
347                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
347-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
347-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
348            </intent-filter>
349            <intent-filter>
349-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
350                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
350-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
350-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
351            </intent-filter>
352        </receiver>
353
354        <provider
354-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:15:9-18:40
355            android:name="com.microsoft.cognitiveservices.speech.util.InternalContentProvider"
355-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:17:13-57
356            android:authorities="com.zara.assistant.MicrosoftCognitiveServicesSpeech.InternalContentProvider"
356-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:16:13-108
357            android:exported="false" />
357-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:18:13-37
358    </application>
359
360</manifest>
