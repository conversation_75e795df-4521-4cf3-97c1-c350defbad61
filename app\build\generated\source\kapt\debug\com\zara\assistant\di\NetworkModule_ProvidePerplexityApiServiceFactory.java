// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import com.zara.assistant.data.remote.api.PerplexityApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvidePerplexityApiServiceFactory implements Factory<PerplexityApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvidePerplexityApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public PerplexityApiService get() {
    return providePerplexityApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvidePerplexityApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvidePerplexityApiServiceFactory(retrofitProvider);
  }

  public static PerplexityApiService providePerplexityApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.providePerplexityApiService(retrofit));
  }
}
