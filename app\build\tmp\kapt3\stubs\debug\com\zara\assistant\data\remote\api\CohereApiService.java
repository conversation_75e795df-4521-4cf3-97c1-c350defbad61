package com.zara.assistant.data.remote.api;

/**
 * Cohere API service interface
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J\u001e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u001e\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/zara/assistant/data/remote/api/CohereApiService;", "", "classifyText", "Lretrofit2/Response;", "Lcom/zara/assistant/data/remote/dto/CohereClassifyResponse;", "request", "Lcom/zara/assistant/data/remote/api/CohereClassifyRequest;", "(Lcom/zara/assistant/data/remote/api/CohereClassifyRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateText", "Lcom/zara/assistant/data/remote/dto/CohereResponse;", "Lcom/zara/assistant/data/remote/dto/CohereGenerateRequest;", "(Lcom/zara/assistant/data/remote/dto/CohereGenerateRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "summarizeText", "Lcom/zara/assistant/data/remote/dto/CohereSummarizeResponse;", "Lcom/zara/assistant/data/remote/api/CohereSummarizeRequest;", "(Lcom/zara/assistant/data/remote/api/CohereSummarizeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface CohereApiService {
    
    @retrofit2.http.POST(value = "v1/generate")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object generateText(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.dto.CohereGenerateRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.zara.assistant.data.remote.dto.CohereResponse>> $completion);
    
    @retrofit2.http.POST(value = "v1/classify")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object classifyText(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.api.CohereClassifyRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.zara.assistant.data.remote.dto.CohereClassifyResponse>> $completion);
    
    @retrofit2.http.POST(value = "v1/summarize")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object summarizeText(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.api.CohereSummarizeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.zara.assistant.data.remote.dto.CohereSummarizeResponse>> $completion);
}