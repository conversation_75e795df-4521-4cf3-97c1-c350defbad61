// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import com.zara.assistant.data.local.dao.SettingsDao;
import com.zara.assistant.data.local.database.ZaraDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideSettingsDaoFactory implements Factory<SettingsDao> {
  private final Provider<ZaraDatabase> databaseProvider;

  public DatabaseModule_ProvideSettingsDaoFactory(Provider<ZaraDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public SettingsDao get() {
    return provideSettingsDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideSettingsDaoFactory create(
      Provider<ZaraDatabase> databaseProvider) {
    return new DatabaseModule_ProvideSettingsDaoFactory(databaseProvider);
  }

  public static SettingsDao provideSettingsDao(ZaraDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSettingsDao(database));
  }
}
