// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import com.zara.assistant.domain.repository.AIRepository;
import com.zara.assistant.domain.repository.ConversationRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AIProcessingService_MembersInjector implements MembersInjector<AIProcessingService> {
  private final Provider<AIRepository> aiRepositoryProvider;

  private final Provider<ConversationRepository> conversationRepositoryProvider;

  public AIProcessingService_MembersInjector(Provider<AIRepository> aiRepositoryProvider,
      Provider<ConversationRepository> conversationRepositoryProvider) {
    this.aiRepositoryProvider = aiRepositoryProvider;
    this.conversationRepositoryProvider = conversationRepositoryProvider;
  }

  public static MembersInjector<AIProcessingService> create(
      Provider<AIRepository> aiRepositoryProvider,
      Provider<ConversationRepository> conversationRepositoryProvider) {
    return new AIProcessingService_MembersInjector(aiRepositoryProvider, conversationRepositoryProvider);
  }

  @Override
  public void injectMembers(AIProcessingService instance) {
    injectAiRepository(instance, aiRepositoryProvider.get());
    injectConversationRepository(instance, conversationRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.services.AIProcessingService.aiRepository")
  public static void injectAiRepository(AIProcessingService instance, AIRepository aiRepository) {
    instance.aiRepository = aiRepository;
  }

  @InjectedFieldSignature("com.zara.assistant.services.AIProcessingService.conversationRepository")
  public static void injectConversationRepository(AIProcessingService instance,
      ConversationRepository conversationRepository) {
    instance.conversationRepository = conversationRepository;
  }
}
