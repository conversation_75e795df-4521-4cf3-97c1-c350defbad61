// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import com.zara.assistant.data.local.dao.UserLearningDao;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserLearningService_MembersInjector implements MembersInjector<UserLearningService> {
  private final Provider<UserLearningDao> userLearningDaoProvider;

  public UserLearningService_MembersInjector(Provider<UserLearningDao> userLearningDaoProvider) {
    this.userLearningDaoProvider = userLearningDaoProvider;
  }

  public static MembersInjector<UserLearningService> create(
      Provider<UserLearningDao> userLearningDaoProvider) {
    return new UserLearningService_MembersInjector(userLearningDaoProvider);
  }

  @Override
  public void injectMembers(UserLearningService instance) {
    injectUserLearningDao(instance, userLearningDaoProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.services.UserLearningService.userLearningDao")
  public static void injectUserLearningDao(UserLearningService instance,
      UserLearningDao userLearningDao) {
    instance.userLearningDao = userLearningDao;
  }
}
