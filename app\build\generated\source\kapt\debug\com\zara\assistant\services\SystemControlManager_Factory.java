// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemControlManager_Factory implements Factory<SystemControlManager> {
  private final Provider<Context> contextProvider;

  public SystemControlManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SystemControlManager get() {
    return newInstance(contextProvider.get());
  }

  public static SystemControlManager_Factory create(Provider<Context> contextProvider) {
    return new SystemControlManager_Factory(contextProvider);
  }

  public static SystemControlManager newInstance(Context context) {
    return new SystemControlManager(context);
  }
}
