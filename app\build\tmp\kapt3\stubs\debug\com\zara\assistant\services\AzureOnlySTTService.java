package com.zara.assistant.services;

/**
 * Azure-Only Speech-to-Text Service
 * Completely replaces Android's built-in SpeechRecognizer
 * Uses Microsoft Azure Speech Services exclusively
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0010\b\u0007\u0018\u0000 ,2\u00020\u0001:\u0002,-B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u001d\u001a\u00020\u001eJ\u0006\u0010\u001f\u001a\u00020\u001eJ\u0006\u0010 \u001a\u00020\tJ\u0006\u0010!\u001a\u00020\tJ\u0006\u0010\u0010\u001a\u00020\tJ\u0006\u0010\u0011\u001a\u00020\tJ\u000e\u0010\"\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020\u000bJ\u000e\u0010$\u001a\u00020\u001e2\u0006\u0010%\u001a\u00020\tJ\u0018\u0010&\u001a\u00020\u001e2\u0006\u0010\'\u001a\u00020\u001c2\u0006\u0010(\u001a\u00020\u000fH\u0002J\u0018\u0010)\u001a\u00020\t2\u0006\u0010(\u001a\u00020\u000f2\b\b\u0002\u0010*\u001a\u00020\tJ\u0006\u0010+\u001a\u00020\u001eR\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0014R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006."}, d2 = {"Lcom/zara/assistant/services/AzureOnlySTTService;", "", "context", "Landroid/content/Context;", "apiKeyManager", "Lcom/zara/assistant/utils/ApiKeyManager;", "(Landroid/content/Context;Lcom/zara/assistant/utils/ApiKeyManager;)V", "_isListening", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_partialText", "", "audioConfig", "Lcom/microsoft/cognitiveservices/speech/audio/AudioConfig;", "currentListener", "Lcom/zara/assistant/services/AzureOnlySTTService$STTListener;", "isInitialized", "isListening", "isListeningFlow", "Lkotlinx/coroutines/flow/StateFlow;", "()Lkotlinx/coroutines/flow/StateFlow;", "partialTextFlow", "getPartialTextFlow", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "speechConfig", "Lcom/microsoft/cognitiveservices/speech/SpeechConfig;", "speechRecognizer", "Lcom/microsoft/cognitiveservices/speech/SpeechRecognizer;", "cancel", "", "destroy", "initialize", "isAvailable", "setLanguage", "language", "setProfanityFilter", "enabled", "setupEventHandlers", "recognizer", "listener", "startListening", "continuous", "stopListening", "Companion", "STTListener", "app_debug"})
public final class AzureOnlySTTService {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.utils.ApiKeyManager apiKeyManager = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AzureOnlySTT";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_LANGUAGE = "en-US";
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.SpeechRecognizer speechRecognizer;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.SpeechConfig speechConfig;
    @org.jetbrains.annotations.Nullable()
    private com.microsoft.cognitiveservices.speech.audio.AudioConfig audioConfig;
    private boolean isInitialized = false;
    private boolean isListening = false;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.Nullable()
    private com.zara.assistant.services.AzureOnlySTTService.STTListener currentListener;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isListening = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListeningFlow = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _partialText = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> partialTextFlow = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.AzureOnlySTTService.Companion Companion = null;
    
    @javax.inject.Inject()
    public AzureOnlySTTService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.utils.ApiKeyManager apiKeyManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isListeningFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getPartialTextFlow() {
        return null;
    }
    
    /**
     * Initialize Azure Speech STT Service with actual API keys
     */
    public final boolean initialize() {
        return false;
    }
    
    /**
     * Start listening with a specific listener
     */
    public final boolean startListening(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.AzureOnlySTTService.STTListener listener, boolean continuous) {
        return false;
    }
    
    /**
     * Setup event handlers for Azure Speech recognizer
     */
    private final void setupEventHandlers(com.microsoft.cognitiveservices.speech.SpeechRecognizer recognizer, com.zara.assistant.services.AzureOnlySTTService.STTListener listener) {
    }
    
    /**
     * Stop listening
     */
    public final void stopListening() {
    }
    
    /**
     * Cancel current recognition
     */
    public final void cancel() {
    }
    
    /**
     * Cleanup resources
     */
    public final void destroy() {
    }
    
    /**
     * Check if currently listening
     */
    public final boolean isListening() {
        return false;
    }
    
    /**
     * Check if initialized
     */
    public final boolean isInitialized() {
        return false;
    }
    
    /**
     * Check if Azure Speech is available
     */
    public final boolean isAvailable() {
        return false;
    }
    
    /**
     * Set recognition language
     */
    public final void setLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String language) {
    }
    
    /**
     * Enable/disable profanity filtering
     */
    public final void setProfanityFilter(boolean enabled) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/zara/assistant/services/AzureOnlySTTService$Companion;", "", "()V", "DEFAULT_LANGUAGE", "", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u0005H&J\b\u0010\b\u001a\u00020\u0003H&J\b\u0010\t\u001a\u00020\u0003H&J\u0010\u0010\n\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u0005H&J\b\u0010\u000b\u001a\u00020\u0003H&\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/services/AzureOnlySTTService$STTListener;", "", "onError", "", "error", "", "onFinalResult", "text", "onListeningStarted", "onListeningStopped", "onPartialResult", "onReady", "app_debug"})
    public static abstract interface STTListener {
        
        public abstract void onReady();
        
        public abstract void onListeningStarted();
        
        public abstract void onPartialResult(@org.jetbrains.annotations.NotNull()
        java.lang.String text);
        
        public abstract void onFinalResult(@org.jetbrains.annotations.NotNull()
        java.lang.String text);
        
        public abstract void onListeningStopped();
        
        public abstract void onError(@org.jetbrains.annotations.NotNull()
        java.lang.String error);
    }
}