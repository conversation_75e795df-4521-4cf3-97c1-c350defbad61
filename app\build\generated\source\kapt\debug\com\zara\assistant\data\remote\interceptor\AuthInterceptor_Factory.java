// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.data.remote.interceptor;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthInterceptor_Factory implements Factory<AuthInterceptor> {
  private final Provider<String> headerNameProvider;

  private final Provider<String> headerValueProvider;

  public AuthInterceptor_Factory(Provider<String> headerNameProvider,
      Provider<String> headerValueProvider) {
    this.headerNameProvider = headerNameProvider;
    this.headerValueProvider = headerValueProvider;
  }

  @Override
  public AuthInterceptor get() {
    return newInstance(headerNameProvider.get(), headerValueProvider.get());
  }

  public static AuthInterceptor_Factory create(Provider<String> headerNameProvider,
      Provider<String> headerValueProvider) {
    return new AuthInterceptor_Factory(headerNameProvider, headerValueProvider);
  }

  public static AuthInterceptor newInstance(String headerName, String headerValue) {
    return new AuthInterceptor(headerName, headerValue);
  }
}
