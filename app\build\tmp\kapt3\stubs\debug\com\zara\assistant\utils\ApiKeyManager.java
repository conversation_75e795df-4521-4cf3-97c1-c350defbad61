package com.zara.assistant.utils;

/**
 * Manager for securely loading API keys from assets
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0003\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0007\u001a\u00020\bJ\u0012\u0010\t\u001a\u0004\u0018\u00010\n2\u0006\u0010\u000b\u001a\u00020\nH\u0002J\u0006\u0010\f\u001a\u00020\nJ\u0006\u0010\r\u001a\u00020\nJ\u0006\u0010\u000e\u001a\u00020\nJ\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u0010J\u0006\u0010\u0011\u001a\u00020\nJ\u0006\u0010\u0012\u001a\u00020\nJ\u0006\u0010\u0013\u001a\u00020\nJ\u0006\u0010\u0014\u001a\u00020\u0015J\b\u0010\u0016\u001a\u00020\u0015H\u0002R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/zara/assistant/utils/ApiKeyManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "apiKeys", "Ljava/util/Properties;", "areApiKeysConfigured", "", "getApiKey", "", "keyName", "getAzureSpeechKey", "getAzureSpeechRegion", "getCohereApiKey", "getMissingApiKeys", "", "getOpenAiApiKey", "getPerplexityApiKey", "getPorcupineAccessKey", "initialize", "", "initializeFallback", "Companion", "app_debug"})
public final class ApiKeyManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ApiKeyManager";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_KEYS_FILE = "api_keys.key";
    @org.jetbrains.annotations.Nullable()
    private java.util.Properties apiKeys;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.utils.ApiKeyManager.Companion Companion = null;
    
    public ApiKeyManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Initialize API keys from assets
     */
    public final void initialize() {
    }
    
    /**
     * Fallback to default values
     */
    private final void initializeFallback() {
    }
    
    /**
     * Get Cohere API key
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCohereApiKey() {
        return null;
    }
    
    /**
     * Get Perplexity API key
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPerplexityApiKey() {
        return null;
    }
    
    /**
     * Get Porcupine access key
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPorcupineAccessKey() {
        return null;
    }
    
    /**
     * Get Azure Speech API key
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAzureSpeechKey() {
        return null;
    }
    
    /**
     * Get Azure Speech region
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAzureSpeechRegion() {
        return null;
    }
    
    /**
     * Get OpenAI API key (optional)
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOpenAiApiKey() {
        return null;
    }
    
    /**
     * Get any API key by name
     */
    private final java.lang.String getApiKey(java.lang.String keyName) {
        return null;
    }
    
    /**
     * Check if all required API keys are configured
     */
    public final boolean areApiKeysConfigured() {
        return false;
    }
    
    /**
     * Get list of missing API keys
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMissingApiKeys() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/zara/assistant/utils/ApiKeyManager$Companion;", "", "()V", "API_KEYS_FILE", "", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}