package com.zara.assistant.data.repository;

/**
 * Implementation of VoiceRepository
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001f\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0011\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010\u0012J\u000e\u0010\u0013\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0002\u0010\u0012J\u000e\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\u0016H\u0016J\u000e\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0016H\u0016J\u000e\u0010\u0018\u001a\u00020\u0019H\u0096@\u00a2\u0006\u0002\u0010\u0012J\u000e\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00100\u0016H\u0016J\u000e\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0016H\u0016J\u000e\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0016H\u0016J$\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001f0\u001e2\u0006\u0010 \u001a\u00020!H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J\u001c\u0010$\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010\u0012J\u0016\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020)H\u0096@\u00a2\u0006\u0002\u0010*J$\u0010+\u001a\b\u0012\u0004\u0012\u00020\'0\u001e2\u0006\u0010,\u001a\u00020\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b-\u0010.J\u001c\u0010/\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b0\u0010\u0012J\u001c\u00101\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u0010\u0012J\u001c\u00103\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b4\u0010\u0012J\u001c\u00105\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u0010\u0012J\u001c\u00107\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b8\u0010\u0012J\u001c\u00109\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010\u0012J\u001c\u0010;\u001a\b\u0012\u0004\u0012\u00020\'0\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b<\u0010\u0012J$\u0010=\u001a\b\u0012\u0004\u0012\u00020\'0\u001e2\u0006\u0010>\u001a\u00020\u0014H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010@J$\u0010A\u001a\b\u0012\u0004\u0012\u00020\'0\u001e2\u0006\u0010B\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bC\u0010DJ\u0016\u0010E\u001a\u00020\'2\u0006\u0010F\u001a\u00020\u0010H\u0096@\u00a2\u0006\u0002\u0010GR\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006H"}, d2 = {"Lcom/zara/assistant/data/repository/VoiceRepositoryImpl;", "Lcom/zara/assistant/domain/repository/VoiceRepository;", "context", "Landroid/content/Context;", "textToSpeech", "Landroid/speech/tts/TextToSpeech;", "(Landroid/content/Context;Landroid/speech/tts/TextToSpeech;)V", "_audioLevel", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_isSpeaking", "", "_isWakeWordActive", "_recognizedText", "", "_voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "checkMicrophonePermission", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAudioConfig", "Lcom/zara/assistant/domain/model/AudioConfig;", "getAudioLevel", "Lkotlinx/coroutines/flow/Flow;", "getRecognizedText", "getVoiceSettings", "Lcom/zara/assistant/domain/model/VoiceSettings;", "getVoiceState", "isSpeaking", "isWakeWordDetectionActive", "processVoiceCommand", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/VoiceCommand;", "audioData", "", "processVoiceCommand-gIAlu-s", "([BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "requestMicrophonePermission", "requestMicrophonePermission-IoAF18A", "setVoiceState", "", "newState", "Lcom/zara/assistant/domain/model/VoiceState$State;", "(Lcom/zara/assistant/domain/model/VoiceState$State;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "speak", "text", "speak-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startAudioSession", "startAudioSession-IoAF18A", "startListening", "startListening-IoAF18A", "startWakeWordDetection", "startWakeWordDetection-IoAF18A", "stopAudioSession", "stopAudioSession-IoAF18A", "stopListening", "stopListening-IoAF18A", "stopSpeaking", "stopSpeaking-IoAF18A", "stopWakeWordDetection", "stopWakeWordDetection-IoAF18A", "updateAudioConfig", "config", "updateAudioConfig-gIAlu-s", "(Lcom/zara/assistant/domain/model/AudioConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVoiceSettings", "settings", "updateVoiceSettings-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVoiceState", "state", "(Lcom/zara/assistant/domain/model/VoiceState;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class VoiceRepositoryImpl implements com.zara.assistant.domain.repository.VoiceRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.speech.tts.TextToSpeech textToSpeech = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.VoiceState> _voiceState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isWakeWordActive = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _recognizedText = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isSpeaking = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Float> _audioLevel = null;
    
    @javax.inject.Inject()
    public VoiceRepositoryImpl(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.speech.tts.TextToSpeech textToSpeech) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.VoiceState> getVoiceState() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateVoiceState(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setVoiceState(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState.State newState, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Boolean> isWakeWordDetectionActive() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.String> getRecognizedText() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Boolean> isSpeaking() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getVoiceSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.VoiceSettings> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAudioConfig(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AudioConfig> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object checkMicrophonePermission(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Float> getAudioLevel() {
        return null;
    }
}