// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.services.AIOrchestrationService;
import com.zara.assistant.services.MLPersonalizationService;
import com.zara.assistant.services.PersonalMemoryService;
import com.zara.assistant.services.WebSearchService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideAIOrchestrationServiceFactory implements Factory<AIOrchestrationService> {
  private final Provider<Context> contextProvider;

  private final Provider<PersonalMemoryService> personalMemoryServiceProvider;

  private final Provider<MLPersonalizationService> mlPersonalizationServiceProvider;

  private final Provider<WebSearchService> webSearchServiceProvider;

  public ServiceModule_ProvideAIOrchestrationServiceFactory(Provider<Context> contextProvider,
      Provider<PersonalMemoryService> personalMemoryServiceProvider,
      Provider<MLPersonalizationService> mlPersonalizationServiceProvider,
      Provider<WebSearchService> webSearchServiceProvider) {
    this.contextProvider = contextProvider;
    this.personalMemoryServiceProvider = personalMemoryServiceProvider;
    this.mlPersonalizationServiceProvider = mlPersonalizationServiceProvider;
    this.webSearchServiceProvider = webSearchServiceProvider;
  }

  @Override
  public AIOrchestrationService get() {
    return provideAIOrchestrationService(contextProvider.get(), personalMemoryServiceProvider.get(), mlPersonalizationServiceProvider.get(), webSearchServiceProvider.get());
  }

  public static ServiceModule_ProvideAIOrchestrationServiceFactory create(
      Provider<Context> contextProvider,
      Provider<PersonalMemoryService> personalMemoryServiceProvider,
      Provider<MLPersonalizationService> mlPersonalizationServiceProvider,
      Provider<WebSearchService> webSearchServiceProvider) {
    return new ServiceModule_ProvideAIOrchestrationServiceFactory(contextProvider, personalMemoryServiceProvider, mlPersonalizationServiceProvider, webSearchServiceProvider);
  }

  public static AIOrchestrationService provideAIOrchestrationService(Context context,
      PersonalMemoryService personalMemoryService,
      MLPersonalizationService mlPersonalizationService, WebSearchService webSearchService) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideAIOrchestrationService(context, personalMemoryService, mlPersonalizationService, webSearchService));
  }
}
