package com.zara.assistant.domain.model;

/**
 * Command parameters for different conversation types
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\n\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001BE\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0014\b\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\b\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00060\b\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u0006J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00060\bH\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\bH\u00c6\u0003JI\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0014\b\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00060\bH\u00c6\u0001J\u0006\u0010\u001b\u001a\u00020\u0000J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00060\bJ\u0010\u0010 \u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0014\u001a\u00020\u0006J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\u0006\u0010#\u001a\u00020\u001dJ\t\u0010$\u001a\u00020\u0006H\u00d6\u0001R\u001d\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00060\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010\u00a8\u0006%"}, d2 = {"Lcom/zara/assistant/domain/model/CommandParameters;", "", "intent", "Lcom/zara/assistant/domain/model/ConversationType;", "extractedParams", "", "", "requiredParams", "", "optionalParams", "(Lcom/zara/assistant/domain/model/ConversationType;Ljava/util/Map;Ljava/util/List;Ljava/util/List;)V", "getExtractedParams", "()Ljava/util/Map;", "getIntent", "()Lcom/zara/assistant/domain/model/ConversationType;", "getOptionalParams", "()Ljava/util/List;", "getRequiredParams", "addParameter", "", "key", "value", "component1", "component2", "component3", "component4", "copy", "deepCopy", "equals", "", "other", "getMissingRequiredParams", "getParameter", "hashCode", "", "isComplete", "toString", "app_debug"})
public final class CommandParameters {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.ConversationType intent = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.String> extractedParams = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> requiredParams = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> optionalParams = null;
    
    public CommandParameters(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationType intent, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> extractedParams, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> requiredParams, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> optionalParams) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationType getIntent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> getExtractedParams() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRequiredParams() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getOptionalParams() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMissingRequiredParams() {
        return null;
    }
    
    public final boolean isComplete() {
        return false;
    }
    
    public final void addParameter(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getParameter(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
    
    /**
     * Create a proper deep copy with new MutableMap instance
     */
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters deepCopy() {
        return null;
    }
    
    public CommandParameters() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationType component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters copy(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationType intent, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> extractedParams, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> requiredParams, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> optionalParams) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}