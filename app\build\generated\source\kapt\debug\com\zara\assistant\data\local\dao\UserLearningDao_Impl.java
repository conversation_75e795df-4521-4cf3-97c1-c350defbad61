package com.zara.assistant.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.zara.assistant.domain.model.BehavioralPattern;
import com.zara.assistant.domain.model.ContextualData;
import com.zara.assistant.domain.model.ConversationHistory;
import com.zara.assistant.domain.model.MLModelData;
import com.zara.assistant.domain.model.SearchCache;
import com.zara.assistant.domain.model.UserFavorite;
import com.zara.assistant.domain.model.UserInteraction;
import com.zara.assistant.domain.model.UserPattern;
import com.zara.assistant.domain.model.UserPreference;
import com.zara.assistant.domain.model.UserProfile;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@SuppressWarnings({"unchecked", "deprecation"})
public final class UserLearningDao_Impl implements UserLearningDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserInteraction> __insertionAdapterOfUserInteraction;

  private final EntityInsertionAdapter<UserPattern> __insertionAdapterOfUserPattern;

  private final EntityInsertionAdapter<UserPreference> __insertionAdapterOfUserPreference;

  private final EntityInsertionAdapter<UserProfile> __insertionAdapterOfUserProfile;

  private final EntityInsertionAdapter<ConversationHistory> __insertionAdapterOfConversationHistory;

  private final EntityInsertionAdapter<SearchCache> __insertionAdapterOfSearchCache;

  private final EntityInsertionAdapter<UserFavorite> __insertionAdapterOfUserFavorite;

  private final EntityInsertionAdapter<BehavioralPattern> __insertionAdapterOfBehavioralPattern;

  private final EntityInsertionAdapter<MLModelData> __insertionAdapterOfMLModelData;

  private final EntityInsertionAdapter<ContextualData> __insertionAdapterOfContextualData;

  private final EntityDeletionOrUpdateAdapter<UserPattern> __updateAdapterOfUserPattern;

  private final EntityDeletionOrUpdateAdapter<UserProfile> __updateAdapterOfUserProfile;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldInteractions;

  private final SharedSQLiteStatement __preparedStmtOfDeactivatePattern;

  private final SharedSQLiteStatement __preparedStmtOfDeletePreference;

  private final SharedSQLiteStatement __preparedStmtOfUpdateSearchAccess;

  public UserLearningDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserInteraction = new EntityInsertionAdapter<UserInteraction>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `user_interactions` (`id`,`command`,`response`,`timestamp`,`timeOfDay`,`dayOfWeek`,`success`,`executionTimeMs`,`contextData`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserInteraction entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCommand() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCommand());
        }
        if (entity.getResponse() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getResponse());
        }
        statement.bindLong(4, entity.getTimestamp());
        statement.bindLong(5, entity.getTimeOfDay());
        statement.bindLong(6, entity.getDayOfWeek());
        final int _tmp = entity.getSuccess() ? 1 : 0;
        statement.bindLong(7, _tmp);
        statement.bindLong(8, entity.getExecutionTimeMs());
        if (entity.getContextData() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getContextData());
        }
      }
    };
    this.__insertionAdapterOfUserPattern = new EntityInsertionAdapter<UserPattern>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_patterns` (`id`,`patternType`,`description`,`confidence`,`frequency`,`lastSeen`,`isActive`,`metadata`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPattern entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPatternType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPatternType());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindDouble(4, entity.getConfidence());
        statement.bindLong(5, entity.getFrequency());
        statement.bindLong(6, entity.getLastSeen());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getMetadata() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getMetadata());
        }
      }
    };
    this.__insertionAdapterOfUserPreference = new EntityInsertionAdapter<UserPreference>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_preferences` (`id`,`preferenceKey`,`preferenceValue`,`confidence`,`source`,`lastUpdated`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreference entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPreferenceKey() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPreferenceKey());
        }
        if (entity.getPreferenceValue() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPreferenceValue());
        }
        statement.bindDouble(4, entity.getConfidence());
        if (entity.getSource() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSource());
        }
        statement.bindLong(6, entity.getLastUpdated());
      }
    };
    this.__insertionAdapterOfUserProfile = new EntityInsertionAdapter<UserProfile>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_profile` (`id`,`name`,`nickname`,`age`,`occupation`,`location`,`timezone`,`createdAt`,`lastUpdated`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserProfile entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getNickname() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getNickname());
        }
        if (entity.getAge() == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, entity.getAge());
        }
        if (entity.getOccupation() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getOccupation());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLocation());
        }
        if (entity.getTimezone() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getTimezone());
        }
        statement.bindLong(8, entity.getCreatedAt());
        statement.bindLong(9, entity.getLastUpdated());
      }
    };
    this.__insertionAdapterOfConversationHistory = new EntityInsertionAdapter<ConversationHistory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `conversation_history` (`id`,`sessionId`,`userInput`,`zaraResponse`,`timestamp`,`conversationType`,`success`,`contextData`,`sentiment`,`topics`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ConversationHistory entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getSessionId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getSessionId());
        }
        if (entity.getUserInput() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserInput());
        }
        if (entity.getZaraResponse() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getZaraResponse());
        }
        statement.bindLong(5, entity.getTimestamp());
        if (entity.getConversationType() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getConversationType());
        }
        final int _tmp = entity.getSuccess() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getContextData() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getContextData());
        }
        if (entity.getSentiment() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getSentiment());
        }
        if (entity.getTopics() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getTopics());
        }
      }
    };
    this.__insertionAdapterOfSearchCache = new EntityInsertionAdapter<SearchCache>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `search_cache` (`id`,`query`,`results`,`timestamp`,`source`,`accessCount`,`lastAccessed`) VALUES (nullif(?, 0),?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SearchCache entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getQuery() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getQuery());
        }
        if (entity.getResults() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getResults());
        }
        statement.bindLong(4, entity.getTimestamp());
        if (entity.getSource() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSource());
        }
        statement.bindLong(6, entity.getAccessCount());
        statement.bindLong(7, entity.getLastAccessed());
      }
    };
    this.__insertionAdapterOfUserFavorite = new EntityInsertionAdapter<UserFavorite>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_favorites` (`id`,`category`,`item`,`confidence`,`source`,`createdAt`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserFavorite entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCategory() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategory());
        }
        if (entity.getItem() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getItem());
        }
        statement.bindDouble(4, entity.getConfidence());
        if (entity.getSource() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSource());
        }
        statement.bindLong(6, entity.getCreatedAt());
      }
    };
    this.__insertionAdapterOfBehavioralPattern = new EntityInsertionAdapter<BehavioralPattern>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `behavioral_patterns` (`id`,`patternType`,`description`,`confidence`,`frequency`,`timePattern`,`contextPattern`,`lastSeen`,`isActive`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BehavioralPattern entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPatternType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPatternType());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindDouble(4, entity.getConfidence());
        statement.bindLong(5, entity.getFrequency());
        if (entity.getTimePattern() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTimePattern());
        }
        if (entity.getContextPattern() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getContextPattern());
        }
        statement.bindLong(8, entity.getLastSeen());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(9, _tmp);
      }
    };
    this.__insertionAdapterOfMLModelData = new EntityInsertionAdapter<MLModelData>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `ml_model_data` (`modelName`,`modelVersion`,`trainingData`,`accuracy`,`lastTrained`,`isActive`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final MLModelData entity) {
        if (entity.getModelName() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getModelName());
        }
        if (entity.getModelVersion() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getModelVersion());
        }
        if (entity.getTrainingData() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTrainingData());
        }
        statement.bindDouble(4, entity.getAccuracy());
        statement.bindLong(5, entity.getLastTrained());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(6, _tmp);
      }
    };
    this.__insertionAdapterOfContextualData = new EntityInsertionAdapter<ContextualData>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `contextual_data` (`id`,`timestamp`,`batteryLevel`,`isCharging`,`wifiConnected`,`bluetoothConnected`,`foregroundApp`,`location`,`weatherCondition`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ContextualData entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTimestamp());
        if (entity.getBatteryLevel() == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, entity.getBatteryLevel());
        }
        final Integer _tmp = entity.isCharging() == null ? null : (entity.isCharging() ? 1 : 0);
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Integer _tmp_1 = entity.getWifiConnected() == null ? null : (entity.getWifiConnected() ? 1 : 0);
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        final Integer _tmp_2 = entity.getBluetoothConnected() == null ? null : (entity.getBluetoothConnected() ? 1 : 0);
        if (_tmp_2 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_2);
        }
        if (entity.getForegroundApp() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getForegroundApp());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getLocation());
        }
        if (entity.getWeatherCondition() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getWeatherCondition());
        }
      }
    };
    this.__updateAdapterOfUserPattern = new EntityDeletionOrUpdateAdapter<UserPattern>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_patterns` SET `id` = ?,`patternType` = ?,`description` = ?,`confidence` = ?,`frequency` = ?,`lastSeen` = ?,`isActive` = ?,`metadata` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPattern entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPatternType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPatternType());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindDouble(4, entity.getConfidence());
        statement.bindLong(5, entity.getFrequency());
        statement.bindLong(6, entity.getLastSeen());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getMetadata() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getMetadata());
        }
        statement.bindLong(9, entity.getId());
      }
    };
    this.__updateAdapterOfUserProfile = new EntityDeletionOrUpdateAdapter<UserProfile>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_profile` SET `id` = ?,`name` = ?,`nickname` = ?,`age` = ?,`occupation` = ?,`location` = ?,`timezone` = ?,`createdAt` = ?,`lastUpdated` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserProfile entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getNickname() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getNickname());
        }
        if (entity.getAge() == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, entity.getAge());
        }
        if (entity.getOccupation() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getOccupation());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLocation());
        }
        if (entity.getTimezone() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getTimezone());
        }
        statement.bindLong(8, entity.getCreatedAt());
        statement.bindLong(9, entity.getLastUpdated());
        if (entity.getId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteOldInteractions = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM user_interactions WHERE timestamp < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivatePattern = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_patterns SET isActive = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeletePreference = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM user_preferences WHERE preferenceKey = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateSearchAccess = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE search_cache SET accessCount = accessCount + 1, lastAccessed = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertInteraction(final UserInteraction interaction,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfUserInteraction.insertAndReturnId(interaction);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertPattern(final UserPattern pattern,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfUserPattern.insertAndReturnId(pattern);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertPreference(final UserPreference preference,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfUserPreference.insertAndReturnId(preference);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertUserProfile(final UserProfile profile,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfUserProfile.insertAndReturnId(profile);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertConversationHistory(final ConversationHistory conversation,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfConversationHistory.insertAndReturnId(conversation);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSearchCache(final SearchCache cache,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfSearchCache.insertAndReturnId(cache);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertUserFavorite(final UserFavorite favorite,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfUserFavorite.insertAndReturnId(favorite);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertBehavioralPattern(final BehavioralPattern pattern,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfBehavioralPattern.insertAndReturnId(pattern);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertMLModelData(final MLModelData modelData,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfMLModelData.insertAndReturnId(modelData);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertContextualData(final ContextualData context,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfContextualData.insertAndReturnId(context);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePattern(final UserPattern pattern,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserPattern.handle(pattern);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserProfile(final UserProfile profile,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserProfile.handle(profile);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldInteractions(final long cutoffTime,
      final Continuation<? super Integer> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldInteractions.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffTime);
        try {
          __db.beginTransaction();
          try {
            final Integer _result = _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldInteractions.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deactivatePattern(final long patternId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivatePattern.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, patternId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivatePattern.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePreference(final String key, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeletePreference.acquire();
        int _argIndex = 1;
        if (key == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, key);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeletePreference.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSearchAccess(final long id, final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateSearchAccess.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateSearchAccess.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentInteractions(final int limit,
      final Continuation<? super List<UserInteraction>> $completion) {
    final String _sql = "SELECT * FROM user_interactions ORDER BY timestamp DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserInteraction>>() {
      @Override
      @NonNull
      public List<UserInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "command");
          final int _cursorIndexOfResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "response");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfDayOfWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "dayOfWeek");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfExecutionTimeMs = CursorUtil.getColumnIndexOrThrow(_cursor, "executionTimeMs");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final List<UserInteraction> _result = new ArrayList<UserInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommand;
            if (_cursor.isNull(_cursorIndexOfCommand)) {
              _tmpCommand = null;
            } else {
              _tmpCommand = _cursor.getString(_cursorIndexOfCommand);
            }
            final String _tmpResponse;
            if (_cursor.isNull(_cursorIndexOfResponse)) {
              _tmpResponse = null;
            } else {
              _tmpResponse = _cursor.getString(_cursorIndexOfResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpTimeOfDay;
            _tmpTimeOfDay = _cursor.getInt(_cursorIndexOfTimeOfDay);
            final int _tmpDayOfWeek;
            _tmpDayOfWeek = _cursor.getInt(_cursorIndexOfDayOfWeek);
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final long _tmpExecutionTimeMs;
            _tmpExecutionTimeMs = _cursor.getLong(_cursorIndexOfExecutionTimeMs);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new UserInteraction(_tmpId,_tmpCommand,_tmpResponse,_tmpTimestamp,_tmpTimeOfDay,_tmpDayOfWeek,_tmpSuccess,_tmpExecutionTimeMs,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getInteractionsByTimeRange(final long startTime, final long endTime,
      final Continuation<? super List<UserInteraction>> $completion) {
    final String _sql = "SELECT * FROM user_interactions WHERE timestamp >= ? AND timestamp <= ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserInteraction>>() {
      @Override
      @NonNull
      public List<UserInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "command");
          final int _cursorIndexOfResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "response");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfDayOfWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "dayOfWeek");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfExecutionTimeMs = CursorUtil.getColumnIndexOrThrow(_cursor, "executionTimeMs");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final List<UserInteraction> _result = new ArrayList<UserInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommand;
            if (_cursor.isNull(_cursorIndexOfCommand)) {
              _tmpCommand = null;
            } else {
              _tmpCommand = _cursor.getString(_cursorIndexOfCommand);
            }
            final String _tmpResponse;
            if (_cursor.isNull(_cursorIndexOfResponse)) {
              _tmpResponse = null;
            } else {
              _tmpResponse = _cursor.getString(_cursorIndexOfResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpTimeOfDay;
            _tmpTimeOfDay = _cursor.getInt(_cursorIndexOfTimeOfDay);
            final int _tmpDayOfWeek;
            _tmpDayOfWeek = _cursor.getInt(_cursorIndexOfDayOfWeek);
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final long _tmpExecutionTimeMs;
            _tmpExecutionTimeMs = _cursor.getLong(_cursorIndexOfExecutionTimeMs);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new UserInteraction(_tmpId,_tmpCommand,_tmpResponse,_tmpTimestamp,_tmpTimeOfDay,_tmpDayOfWeek,_tmpSuccess,_tmpExecutionTimeMs,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getInteractionsByHour(final int hour,
      final Continuation<? super List<UserInteraction>> $completion) {
    final String _sql = "SELECT * FROM user_interactions WHERE timeOfDay = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, hour);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserInteraction>>() {
      @Override
      @NonNull
      public List<UserInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "command");
          final int _cursorIndexOfResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "response");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfDayOfWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "dayOfWeek");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfExecutionTimeMs = CursorUtil.getColumnIndexOrThrow(_cursor, "executionTimeMs");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final List<UserInteraction> _result = new ArrayList<UserInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommand;
            if (_cursor.isNull(_cursorIndexOfCommand)) {
              _tmpCommand = null;
            } else {
              _tmpCommand = _cursor.getString(_cursorIndexOfCommand);
            }
            final String _tmpResponse;
            if (_cursor.isNull(_cursorIndexOfResponse)) {
              _tmpResponse = null;
            } else {
              _tmpResponse = _cursor.getString(_cursorIndexOfResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpTimeOfDay;
            _tmpTimeOfDay = _cursor.getInt(_cursorIndexOfTimeOfDay);
            final int _tmpDayOfWeek;
            _tmpDayOfWeek = _cursor.getInt(_cursorIndexOfDayOfWeek);
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final long _tmpExecutionTimeMs;
            _tmpExecutionTimeMs = _cursor.getLong(_cursorIndexOfExecutionTimeMs);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new UserInteraction(_tmpId,_tmpCommand,_tmpResponse,_tmpTimestamp,_tmpTimeOfDay,_tmpDayOfWeek,_tmpSuccess,_tmpExecutionTimeMs,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getInteractionsByDayOfWeek(final int dayOfWeek,
      final Continuation<? super List<UserInteraction>> $completion) {
    final String _sql = "SELECT * FROM user_interactions WHERE dayOfWeek = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, dayOfWeek);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserInteraction>>() {
      @Override
      @NonNull
      public List<UserInteraction> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "command");
          final int _cursorIndexOfResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "response");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfDayOfWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "dayOfWeek");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfExecutionTimeMs = CursorUtil.getColumnIndexOrThrow(_cursor, "executionTimeMs");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final List<UserInteraction> _result = new ArrayList<UserInteraction>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserInteraction _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommand;
            if (_cursor.isNull(_cursorIndexOfCommand)) {
              _tmpCommand = null;
            } else {
              _tmpCommand = _cursor.getString(_cursorIndexOfCommand);
            }
            final String _tmpResponse;
            if (_cursor.isNull(_cursorIndexOfResponse)) {
              _tmpResponse = null;
            } else {
              _tmpResponse = _cursor.getString(_cursorIndexOfResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpTimeOfDay;
            _tmpTimeOfDay = _cursor.getInt(_cursorIndexOfTimeOfDay);
            final int _tmpDayOfWeek;
            _tmpDayOfWeek = _cursor.getInt(_cursorIndexOfDayOfWeek);
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final long _tmpExecutionTimeMs;
            _tmpExecutionTimeMs = _cursor.getLong(_cursorIndexOfExecutionTimeMs);
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            _item = new UserInteraction(_tmpId,_tmpCommand,_tmpResponse,_tmpTimestamp,_tmpTimeOfDay,_tmpDayOfWeek,_tmpSuccess,_tmpExecutionTimeMs,_tmpContextData);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMostFrequentCommands(final int limit,
      final Continuation<? super List<CommandFrequency>> $completion) {
    final String _sql = "SELECT command, COUNT(*) as frequency FROM user_interactions WHERE success = 1 GROUP BY command ORDER BY frequency DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CommandFrequency>>() {
      @Override
      @NonNull
      public List<CommandFrequency> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCommand = 0;
          final int _cursorIndexOfFrequency = 1;
          final List<CommandFrequency> _result = new ArrayList<CommandFrequency>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CommandFrequency _item;
            final String _tmpCommand;
            if (_cursor.isNull(_cursorIndexOfCommand)) {
              _tmpCommand = null;
            } else {
              _tmpCommand = _cursor.getString(_cursorIndexOfCommand);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            _item = new CommandFrequency(_tmpCommand,_tmpFrequency);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getActivePatterns(final Continuation<? super List<UserPattern>> $completion) {
    final String _sql = "SELECT * FROM user_patterns WHERE isActive = 1 ORDER BY confidence DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserPattern>>() {
      @Override
      @NonNull
      public List<UserPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfLastSeen = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSeen");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfMetadata = CursorUtil.getColumnIndexOrThrow(_cursor, "metadata");
          final List<UserPattern> _result = new ArrayList<UserPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final long _tmpLastSeen;
            _tmpLastSeen = _cursor.getLong(_cursorIndexOfLastSeen);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpMetadata;
            if (_cursor.isNull(_cursorIndexOfMetadata)) {
              _tmpMetadata = null;
            } else {
              _tmpMetadata = _cursor.getString(_cursorIndexOfMetadata);
            }
            _item = new UserPattern(_tmpId,_tmpPatternType,_tmpDescription,_tmpConfidence,_tmpFrequency,_tmpLastSeen,_tmpIsActive,_tmpMetadata);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPatternsByType(final String type,
      final Continuation<? super List<UserPattern>> $completion) {
    final String _sql = "SELECT * FROM user_patterns WHERE patternType = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserPattern>>() {
      @Override
      @NonNull
      public List<UserPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfLastSeen = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSeen");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfMetadata = CursorUtil.getColumnIndexOrThrow(_cursor, "metadata");
          final List<UserPattern> _result = new ArrayList<UserPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final long _tmpLastSeen;
            _tmpLastSeen = _cursor.getLong(_cursorIndexOfLastSeen);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpMetadata;
            if (_cursor.isNull(_cursorIndexOfMetadata)) {
              _tmpMetadata = null;
            } else {
              _tmpMetadata = _cursor.getString(_cursorIndexOfMetadata);
            }
            _item = new UserPattern(_tmpId,_tmpPatternType,_tmpDescription,_tmpConfidence,_tmpFrequency,_tmpLastSeen,_tmpIsActive,_tmpMetadata);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getHighConfidencePatterns(final float minConfidence,
      final Continuation<? super List<UserPattern>> $completion) {
    final String _sql = "SELECT * FROM user_patterns WHERE confidence >= ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minConfidence);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserPattern>>() {
      @Override
      @NonNull
      public List<UserPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfLastSeen = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSeen");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfMetadata = CursorUtil.getColumnIndexOrThrow(_cursor, "metadata");
          final List<UserPattern> _result = new ArrayList<UserPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final long _tmpLastSeen;
            _tmpLastSeen = _cursor.getLong(_cursorIndexOfLastSeen);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpMetadata;
            if (_cursor.isNull(_cursorIndexOfMetadata)) {
              _tmpMetadata = null;
            } else {
              _tmpMetadata = _cursor.getString(_cursorIndexOfMetadata);
            }
            _item = new UserPattern(_tmpId,_tmpPatternType,_tmpDescription,_tmpConfidence,_tmpFrequency,_tmpLastSeen,_tmpIsActive,_tmpMetadata);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPreference(final String key,
      final Continuation<? super UserPreference> $completion) {
    final String _sql = "SELECT * FROM user_preferences WHERE preferenceKey = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (key == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, key);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserPreference>() {
      @Override
      @Nullable
      public UserPreference call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPreferenceKey = CursorUtil.getColumnIndexOrThrow(_cursor, "preferenceKey");
          final int _cursorIndexOfPreferenceValue = CursorUtil.getColumnIndexOrThrow(_cursor, "preferenceValue");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfLastUpdated = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdated");
          final UserPreference _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPreferenceKey;
            if (_cursor.isNull(_cursorIndexOfPreferenceKey)) {
              _tmpPreferenceKey = null;
            } else {
              _tmpPreferenceKey = _cursor.getString(_cursorIndexOfPreferenceKey);
            }
            final String _tmpPreferenceValue;
            if (_cursor.isNull(_cursorIndexOfPreferenceValue)) {
              _tmpPreferenceValue = null;
            } else {
              _tmpPreferenceValue = _cursor.getString(_cursorIndexOfPreferenceValue);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final long _tmpLastUpdated;
            _tmpLastUpdated = _cursor.getLong(_cursorIndexOfLastUpdated);
            _result = new UserPreference(_tmpId,_tmpPreferenceKey,_tmpPreferenceValue,_tmpConfidence,_tmpSource,_tmpLastUpdated);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllPreferences(final Continuation<? super List<UserPreference>> $completion) {
    final String _sql = "SELECT * FROM user_preferences ORDER BY confidence DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserPreference>>() {
      @Override
      @NonNull
      public List<UserPreference> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPreferenceKey = CursorUtil.getColumnIndexOrThrow(_cursor, "preferenceKey");
          final int _cursorIndexOfPreferenceValue = CursorUtil.getColumnIndexOrThrow(_cursor, "preferenceValue");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfLastUpdated = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdated");
          final List<UserPreference> _result = new ArrayList<UserPreference>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserPreference _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPreferenceKey;
            if (_cursor.isNull(_cursorIndexOfPreferenceKey)) {
              _tmpPreferenceKey = null;
            } else {
              _tmpPreferenceKey = _cursor.getString(_cursorIndexOfPreferenceKey);
            }
            final String _tmpPreferenceValue;
            if (_cursor.isNull(_cursorIndexOfPreferenceValue)) {
              _tmpPreferenceValue = null;
            } else {
              _tmpPreferenceValue = _cursor.getString(_cursorIndexOfPreferenceValue);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final long _tmpLastUpdated;
            _tmpLastUpdated = _cursor.getLong(_cursorIndexOfLastUpdated);
            _item = new UserPreference(_tmpId,_tmpPreferenceKey,_tmpPreferenceValue,_tmpConfidence,_tmpSource,_tmpLastUpdated);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserProfile(final String userId,
      final Continuation<? super UserProfile> $completion) {
    final String _sql = "SELECT * FROM user_profile WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserProfile>() {
      @Override
      @Nullable
      public UserProfile call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfNickname = CursorUtil.getColumnIndexOrThrow(_cursor, "nickname");
          final int _cursorIndexOfAge = CursorUtil.getColumnIndexOrThrow(_cursor, "age");
          final int _cursorIndexOfOccupation = CursorUtil.getColumnIndexOrThrow(_cursor, "occupation");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfTimezone = CursorUtil.getColumnIndexOrThrow(_cursor, "timezone");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfLastUpdated = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdated");
          final UserProfile _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpNickname;
            if (_cursor.isNull(_cursorIndexOfNickname)) {
              _tmpNickname = null;
            } else {
              _tmpNickname = _cursor.getString(_cursorIndexOfNickname);
            }
            final Integer _tmpAge;
            if (_cursor.isNull(_cursorIndexOfAge)) {
              _tmpAge = null;
            } else {
              _tmpAge = _cursor.getInt(_cursorIndexOfAge);
            }
            final String _tmpOccupation;
            if (_cursor.isNull(_cursorIndexOfOccupation)) {
              _tmpOccupation = null;
            } else {
              _tmpOccupation = _cursor.getString(_cursorIndexOfOccupation);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final String _tmpTimezone;
            if (_cursor.isNull(_cursorIndexOfTimezone)) {
              _tmpTimezone = null;
            } else {
              _tmpTimezone = _cursor.getString(_cursorIndexOfTimezone);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpLastUpdated;
            _tmpLastUpdated = _cursor.getLong(_cursorIndexOfLastUpdated);
            _result = new UserProfile(_tmpId,_tmpName,_tmpNickname,_tmpAge,_tmpOccupation,_tmpLocation,_tmpTimezone,_tmpCreatedAt,_tmpLastUpdated);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentConversations(final int limit,
      final Continuation<? super List<ConversationHistory>> $completion) {
    final String _sql = "SELECT * FROM conversation_history ORDER BY timestamp DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ConversationHistory>>() {
      @Override
      @NonNull
      public List<ConversationHistory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfUserInput = CursorUtil.getColumnIndexOrThrow(_cursor, "userInput");
          final int _cursorIndexOfZaraResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "zaraResponse");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfConversationType = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationType");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final int _cursorIndexOfSentiment = CursorUtil.getColumnIndexOrThrow(_cursor, "sentiment");
          final int _cursorIndexOfTopics = CursorUtil.getColumnIndexOrThrow(_cursor, "topics");
          final List<ConversationHistory> _result = new ArrayList<ConversationHistory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ConversationHistory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpUserInput;
            if (_cursor.isNull(_cursorIndexOfUserInput)) {
              _tmpUserInput = null;
            } else {
              _tmpUserInput = _cursor.getString(_cursorIndexOfUserInput);
            }
            final String _tmpZaraResponse;
            if (_cursor.isNull(_cursorIndexOfZaraResponse)) {
              _tmpZaraResponse = null;
            } else {
              _tmpZaraResponse = _cursor.getString(_cursorIndexOfZaraResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpConversationType;
            if (_cursor.isNull(_cursorIndexOfConversationType)) {
              _tmpConversationType = null;
            } else {
              _tmpConversationType = _cursor.getString(_cursorIndexOfConversationType);
            }
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            final String _tmpSentiment;
            if (_cursor.isNull(_cursorIndexOfSentiment)) {
              _tmpSentiment = null;
            } else {
              _tmpSentiment = _cursor.getString(_cursorIndexOfSentiment);
            }
            final String _tmpTopics;
            if (_cursor.isNull(_cursorIndexOfTopics)) {
              _tmpTopics = null;
            } else {
              _tmpTopics = _cursor.getString(_cursorIndexOfTopics);
            }
            _item = new ConversationHistory(_tmpId,_tmpSessionId,_tmpUserInput,_tmpZaraResponse,_tmpTimestamp,_tmpConversationType,_tmpSuccess,_tmpContextData,_tmpSentiment,_tmpTopics);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getConversationsBySession(final String sessionId,
      final Continuation<? super List<ConversationHistory>> $completion) {
    final String _sql = "SELECT * FROM conversation_history WHERE sessionId = ? ORDER BY timestamp";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (sessionId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, sessionId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ConversationHistory>>() {
      @Override
      @NonNull
      public List<ConversationHistory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfUserInput = CursorUtil.getColumnIndexOrThrow(_cursor, "userInput");
          final int _cursorIndexOfZaraResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "zaraResponse");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfConversationType = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationType");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final int _cursorIndexOfSentiment = CursorUtil.getColumnIndexOrThrow(_cursor, "sentiment");
          final int _cursorIndexOfTopics = CursorUtil.getColumnIndexOrThrow(_cursor, "topics");
          final List<ConversationHistory> _result = new ArrayList<ConversationHistory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ConversationHistory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpUserInput;
            if (_cursor.isNull(_cursorIndexOfUserInput)) {
              _tmpUserInput = null;
            } else {
              _tmpUserInput = _cursor.getString(_cursorIndexOfUserInput);
            }
            final String _tmpZaraResponse;
            if (_cursor.isNull(_cursorIndexOfZaraResponse)) {
              _tmpZaraResponse = null;
            } else {
              _tmpZaraResponse = _cursor.getString(_cursorIndexOfZaraResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpConversationType;
            if (_cursor.isNull(_cursorIndexOfConversationType)) {
              _tmpConversationType = null;
            } else {
              _tmpConversationType = _cursor.getString(_cursorIndexOfConversationType);
            }
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            final String _tmpSentiment;
            if (_cursor.isNull(_cursorIndexOfSentiment)) {
              _tmpSentiment = null;
            } else {
              _tmpSentiment = _cursor.getString(_cursorIndexOfSentiment);
            }
            final String _tmpTopics;
            if (_cursor.isNull(_cursorIndexOfTopics)) {
              _tmpTopics = null;
            } else {
              _tmpTopics = _cursor.getString(_cursorIndexOfTopics);
            }
            _item = new ConversationHistory(_tmpId,_tmpSessionId,_tmpUserInput,_tmpZaraResponse,_tmpTimestamp,_tmpConversationType,_tmpSuccess,_tmpContextData,_tmpSentiment,_tmpTopics);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getConversationsByTopic(final String topic,
      final Continuation<? super List<ConversationHistory>> $completion) {
    final String _sql = "SELECT * FROM conversation_history WHERE topics LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (topic == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, topic);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ConversationHistory>>() {
      @Override
      @NonNull
      public List<ConversationHistory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfUserInput = CursorUtil.getColumnIndexOrThrow(_cursor, "userInput");
          final int _cursorIndexOfZaraResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "zaraResponse");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfConversationType = CursorUtil.getColumnIndexOrThrow(_cursor, "conversationType");
          final int _cursorIndexOfSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "success");
          final int _cursorIndexOfContextData = CursorUtil.getColumnIndexOrThrow(_cursor, "contextData");
          final int _cursorIndexOfSentiment = CursorUtil.getColumnIndexOrThrow(_cursor, "sentiment");
          final int _cursorIndexOfTopics = CursorUtil.getColumnIndexOrThrow(_cursor, "topics");
          final List<ConversationHistory> _result = new ArrayList<ConversationHistory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ConversationHistory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpUserInput;
            if (_cursor.isNull(_cursorIndexOfUserInput)) {
              _tmpUserInput = null;
            } else {
              _tmpUserInput = _cursor.getString(_cursorIndexOfUserInput);
            }
            final String _tmpZaraResponse;
            if (_cursor.isNull(_cursorIndexOfZaraResponse)) {
              _tmpZaraResponse = null;
            } else {
              _tmpZaraResponse = _cursor.getString(_cursorIndexOfZaraResponse);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpConversationType;
            if (_cursor.isNull(_cursorIndexOfConversationType)) {
              _tmpConversationType = null;
            } else {
              _tmpConversationType = _cursor.getString(_cursorIndexOfConversationType);
            }
            final boolean _tmpSuccess;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSuccess);
            _tmpSuccess = _tmp != 0;
            final String _tmpContextData;
            if (_cursor.isNull(_cursorIndexOfContextData)) {
              _tmpContextData = null;
            } else {
              _tmpContextData = _cursor.getString(_cursorIndexOfContextData);
            }
            final String _tmpSentiment;
            if (_cursor.isNull(_cursorIndexOfSentiment)) {
              _tmpSentiment = null;
            } else {
              _tmpSentiment = _cursor.getString(_cursorIndexOfSentiment);
            }
            final String _tmpTopics;
            if (_cursor.isNull(_cursorIndexOfTopics)) {
              _tmpTopics = null;
            } else {
              _tmpTopics = _cursor.getString(_cursorIndexOfTopics);
            }
            _item = new ConversationHistory(_tmpId,_tmpSessionId,_tmpUserInput,_tmpZaraResponse,_tmpTimestamp,_tmpConversationType,_tmpSuccess,_tmpContextData,_tmpSentiment,_tmpTopics);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCachedSearch(final String query,
      final Continuation<? super SearchCache> $completion) {
    final String _sql = "SELECT * FROM search_cache WHERE query = ? ORDER BY timestamp DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SearchCache>() {
      @Override
      @Nullable
      public SearchCache call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfQuery = CursorUtil.getColumnIndexOrThrow(_cursor, "query");
          final int _cursorIndexOfResults = CursorUtil.getColumnIndexOrThrow(_cursor, "results");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfAccessCount = CursorUtil.getColumnIndexOrThrow(_cursor, "accessCount");
          final int _cursorIndexOfLastAccessed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastAccessed");
          final SearchCache _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpQuery;
            if (_cursor.isNull(_cursorIndexOfQuery)) {
              _tmpQuery = null;
            } else {
              _tmpQuery = _cursor.getString(_cursorIndexOfQuery);
            }
            final String _tmpResults;
            if (_cursor.isNull(_cursorIndexOfResults)) {
              _tmpResults = null;
            } else {
              _tmpResults = _cursor.getString(_cursorIndexOfResults);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final int _tmpAccessCount;
            _tmpAccessCount = _cursor.getInt(_cursorIndexOfAccessCount);
            final long _tmpLastAccessed;
            _tmpLastAccessed = _cursor.getLong(_cursorIndexOfLastAccessed);
            _result = new SearchCache(_tmpId,_tmpQuery,_tmpResults,_tmpTimestamp,_tmpSource,_tmpAccessCount,_tmpLastAccessed);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPopularSearches(final int limit,
      final Continuation<? super List<SearchCache>> $completion) {
    final String _sql = "SELECT * FROM search_cache ORDER BY accessCount DESC, timestamp DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SearchCache>>() {
      @Override
      @NonNull
      public List<SearchCache> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfQuery = CursorUtil.getColumnIndexOrThrow(_cursor, "query");
          final int _cursorIndexOfResults = CursorUtil.getColumnIndexOrThrow(_cursor, "results");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfAccessCount = CursorUtil.getColumnIndexOrThrow(_cursor, "accessCount");
          final int _cursorIndexOfLastAccessed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastAccessed");
          final List<SearchCache> _result = new ArrayList<SearchCache>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SearchCache _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpQuery;
            if (_cursor.isNull(_cursorIndexOfQuery)) {
              _tmpQuery = null;
            } else {
              _tmpQuery = _cursor.getString(_cursorIndexOfQuery);
            }
            final String _tmpResults;
            if (_cursor.isNull(_cursorIndexOfResults)) {
              _tmpResults = null;
            } else {
              _tmpResults = _cursor.getString(_cursorIndexOfResults);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final int _tmpAccessCount;
            _tmpAccessCount = _cursor.getInt(_cursorIndexOfAccessCount);
            final long _tmpLastAccessed;
            _tmpLastAccessed = _cursor.getLong(_cursorIndexOfLastAccessed);
            _item = new SearchCache(_tmpId,_tmpQuery,_tmpResults,_tmpTimestamp,_tmpSource,_tmpAccessCount,_tmpLastAccessed);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getFavoritesByCategory(final String category,
      final Continuation<? super List<UserFavorite>> $completion) {
    final String _sql = "SELECT * FROM user_favorites WHERE category = ? ORDER BY confidence DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserFavorite>>() {
      @Override
      @NonNull
      public List<UserFavorite> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItem = CursorUtil.getColumnIndexOrThrow(_cursor, "item");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<UserFavorite> _result = new ArrayList<UserFavorite>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserFavorite _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItem;
            if (_cursor.isNull(_cursorIndexOfItem)) {
              _tmpItem = null;
            } else {
              _tmpItem = _cursor.getString(_cursorIndexOfItem);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new UserFavorite(_tmpId,_tmpCategory,_tmpItem,_tmpConfidence,_tmpSource,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTopFavorites(final int limit,
      final Continuation<? super List<UserFavorite>> $completion) {
    final String _sql = "SELECT * FROM user_favorites ORDER BY confidence DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<UserFavorite>>() {
      @Override
      @NonNull
      public List<UserFavorite> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItem = CursorUtil.getColumnIndexOrThrow(_cursor, "item");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<UserFavorite> _result = new ArrayList<UserFavorite>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final UserFavorite _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItem;
            if (_cursor.isNull(_cursorIndexOfItem)) {
              _tmpItem = null;
            } else {
              _tmpItem = _cursor.getString(_cursorIndexOfItem);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new UserFavorite(_tmpId,_tmpCategory,_tmpItem,_tmpConfidence,_tmpSource,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getActiveBehavioralPatterns(
      final Continuation<? super List<BehavioralPattern>> $completion) {
    final String _sql = "SELECT * FROM behavioral_patterns WHERE isActive = 1 ORDER BY confidence DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<BehavioralPattern>>() {
      @Override
      @NonNull
      public List<BehavioralPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfTimePattern = CursorUtil.getColumnIndexOrThrow(_cursor, "timePattern");
          final int _cursorIndexOfContextPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "contextPattern");
          final int _cursorIndexOfLastSeen = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSeen");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BehavioralPattern> _result = new ArrayList<BehavioralPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BehavioralPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final String _tmpTimePattern;
            if (_cursor.isNull(_cursorIndexOfTimePattern)) {
              _tmpTimePattern = null;
            } else {
              _tmpTimePattern = _cursor.getString(_cursorIndexOfTimePattern);
            }
            final String _tmpContextPattern;
            if (_cursor.isNull(_cursorIndexOfContextPattern)) {
              _tmpContextPattern = null;
            } else {
              _tmpContextPattern = _cursor.getString(_cursorIndexOfContextPattern);
            }
            final long _tmpLastSeen;
            _tmpLastSeen = _cursor.getLong(_cursorIndexOfLastSeen);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new BehavioralPattern(_tmpId,_tmpPatternType,_tmpDescription,_tmpConfidence,_tmpFrequency,_tmpTimePattern,_tmpContextPattern,_tmpLastSeen,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getBehavioralPatternsByType(final String type,
      final Continuation<? super List<BehavioralPattern>> $completion) {
    final String _sql = "SELECT * FROM behavioral_patterns WHERE patternType = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<BehavioralPattern>>() {
      @Override
      @NonNull
      public List<BehavioralPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfTimePattern = CursorUtil.getColumnIndexOrThrow(_cursor, "timePattern");
          final int _cursorIndexOfContextPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "contextPattern");
          final int _cursorIndexOfLastSeen = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSeen");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BehavioralPattern> _result = new ArrayList<BehavioralPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BehavioralPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final String _tmpTimePattern;
            if (_cursor.isNull(_cursorIndexOfTimePattern)) {
              _tmpTimePattern = null;
            } else {
              _tmpTimePattern = _cursor.getString(_cursorIndexOfTimePattern);
            }
            final String _tmpContextPattern;
            if (_cursor.isNull(_cursorIndexOfContextPattern)) {
              _tmpContextPattern = null;
            } else {
              _tmpContextPattern = _cursor.getString(_cursorIndexOfContextPattern);
            }
            final long _tmpLastSeen;
            _tmpLastSeen = _cursor.getLong(_cursorIndexOfLastSeen);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new BehavioralPattern(_tmpId,_tmpPatternType,_tmpDescription,_tmpConfidence,_tmpFrequency,_tmpTimePattern,_tmpContextPattern,_tmpLastSeen,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMLModelData(final String modelName,
      final Continuation<? super MLModelData> $completion) {
    final String _sql = "SELECT * FROM ml_model_data WHERE modelName = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (modelName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, modelName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<MLModelData>() {
      @Override
      @Nullable
      public MLModelData call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfModelName = CursorUtil.getColumnIndexOrThrow(_cursor, "modelName");
          final int _cursorIndexOfModelVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "modelVersion");
          final int _cursorIndexOfTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "trainingData");
          final int _cursorIndexOfAccuracy = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracy");
          final int _cursorIndexOfLastTrained = CursorUtil.getColumnIndexOrThrow(_cursor, "lastTrained");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final MLModelData _result;
          if (_cursor.moveToFirst()) {
            final String _tmpModelName;
            if (_cursor.isNull(_cursorIndexOfModelName)) {
              _tmpModelName = null;
            } else {
              _tmpModelName = _cursor.getString(_cursorIndexOfModelName);
            }
            final String _tmpModelVersion;
            if (_cursor.isNull(_cursorIndexOfModelVersion)) {
              _tmpModelVersion = null;
            } else {
              _tmpModelVersion = _cursor.getString(_cursorIndexOfModelVersion);
            }
            final String _tmpTrainingData;
            if (_cursor.isNull(_cursorIndexOfTrainingData)) {
              _tmpTrainingData = null;
            } else {
              _tmpTrainingData = _cursor.getString(_cursorIndexOfTrainingData);
            }
            final float _tmpAccuracy;
            _tmpAccuracy = _cursor.getFloat(_cursorIndexOfAccuracy);
            final long _tmpLastTrained;
            _tmpLastTrained = _cursor.getLong(_cursorIndexOfLastTrained);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _result = new MLModelData(_tmpModelName,_tmpModelVersion,_tmpTrainingData,_tmpAccuracy,_tmpLastTrained,_tmpIsActive);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllActiveModels(final Continuation<? super List<MLModelData>> $completion) {
    final String _sql = "SELECT * FROM ml_model_data WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<MLModelData>>() {
      @Override
      @NonNull
      public List<MLModelData> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfModelName = CursorUtil.getColumnIndexOrThrow(_cursor, "modelName");
          final int _cursorIndexOfModelVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "modelVersion");
          final int _cursorIndexOfTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "trainingData");
          final int _cursorIndexOfAccuracy = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracy");
          final int _cursorIndexOfLastTrained = CursorUtil.getColumnIndexOrThrow(_cursor, "lastTrained");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<MLModelData> _result = new ArrayList<MLModelData>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final MLModelData _item;
            final String _tmpModelName;
            if (_cursor.isNull(_cursorIndexOfModelName)) {
              _tmpModelName = null;
            } else {
              _tmpModelName = _cursor.getString(_cursorIndexOfModelName);
            }
            final String _tmpModelVersion;
            if (_cursor.isNull(_cursorIndexOfModelVersion)) {
              _tmpModelVersion = null;
            } else {
              _tmpModelVersion = _cursor.getString(_cursorIndexOfModelVersion);
            }
            final String _tmpTrainingData;
            if (_cursor.isNull(_cursorIndexOfTrainingData)) {
              _tmpTrainingData = null;
            } else {
              _tmpTrainingData = _cursor.getString(_cursorIndexOfTrainingData);
            }
            final float _tmpAccuracy;
            _tmpAccuracy = _cursor.getFloat(_cursorIndexOfAccuracy);
            final long _tmpLastTrained;
            _tmpLastTrained = _cursor.getLong(_cursorIndexOfLastTrained);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new MLModelData(_tmpModelName,_tmpModelVersion,_tmpTrainingData,_tmpAccuracy,_tmpLastTrained,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentContextualData(final int limit,
      final Continuation<? super List<ContextualData>> $completion) {
    final String _sql = "SELECT * FROM contextual_data ORDER BY timestamp DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ContextualData>>() {
      @Override
      @NonNull
      public List<ContextualData> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfBatteryLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "batteryLevel");
          final int _cursorIndexOfIsCharging = CursorUtil.getColumnIndexOrThrow(_cursor, "isCharging");
          final int _cursorIndexOfWifiConnected = CursorUtil.getColumnIndexOrThrow(_cursor, "wifiConnected");
          final int _cursorIndexOfBluetoothConnected = CursorUtil.getColumnIndexOrThrow(_cursor, "bluetoothConnected");
          final int _cursorIndexOfForegroundApp = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundApp");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfWeatherCondition = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherCondition");
          final List<ContextualData> _result = new ArrayList<ContextualData>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ContextualData _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final Integer _tmpBatteryLevel;
            if (_cursor.isNull(_cursorIndexOfBatteryLevel)) {
              _tmpBatteryLevel = null;
            } else {
              _tmpBatteryLevel = _cursor.getInt(_cursorIndexOfBatteryLevel);
            }
            final Boolean _tmpIsCharging;
            final Integer _tmp;
            if (_cursor.isNull(_cursorIndexOfIsCharging)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(_cursorIndexOfIsCharging);
            }
            _tmpIsCharging = _tmp == null ? null : _tmp != 0;
            final Boolean _tmpWifiConnected;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWifiConnected)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWifiConnected);
            }
            _tmpWifiConnected = _tmp_1 == null ? null : _tmp_1 != 0;
            final Boolean _tmpBluetoothConnected;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfBluetoothConnected)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfBluetoothConnected);
            }
            _tmpBluetoothConnected = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpForegroundApp;
            if (_cursor.isNull(_cursorIndexOfForegroundApp)) {
              _tmpForegroundApp = null;
            } else {
              _tmpForegroundApp = _cursor.getString(_cursorIndexOfForegroundApp);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final String _tmpWeatherCondition;
            if (_cursor.isNull(_cursorIndexOfWeatherCondition)) {
              _tmpWeatherCondition = null;
            } else {
              _tmpWeatherCondition = _cursor.getString(_cursorIndexOfWeatherCondition);
            }
            _item = new ContextualData(_tmpId,_tmpTimestamp,_tmpBatteryLevel,_tmpIsCharging,_tmpWifiConnected,_tmpBluetoothConnected,_tmpForegroundApp,_tmpLocation,_tmpWeatherCondition);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getInteractionCountSince(final long startTime,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM user_interactions WHERE timestamp >= ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageExecutionTime(final long startTime,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(executionTimeMs) FROM user_interactions WHERE success = 1 AND timestamp >= ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getSuccessRate(final long startTime,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT (COUNT(CASE WHEN success = 1 THEN 1 END) * 100.0 / COUNT(*)) as successRate FROM user_interactions WHERE timestamp >= ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
