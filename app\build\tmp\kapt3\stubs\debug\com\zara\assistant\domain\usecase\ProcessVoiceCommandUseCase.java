package com.zara.assistant.domain.usecase;

/**
 * Use case for processing voice commands end-to-end
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0082@\u00a2\u0006\u0002\u0010\u000eJ$\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u0013H\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0016"}, d2 = {"Lcom/zara/assistant/domain/usecase/ProcessVoiceCommandUseCase;", "", "voiceRepository", "Lcom/zara/assistant/domain/repository/VoiceRepository;", "aiRepository", "Lcom/zara/assistant/domain/repository/AIRepository;", "conversationRepository", "Lcom/zara/assistant/domain/repository/ConversationRepository;", "(Lcom/zara/assistant/domain/repository/VoiceRepository;Lcom/zara/assistant/domain/repository/AIRepository;Lcom/zara/assistant/domain/repository/ConversationRepository;)V", "executeSystemActions", "", "actions", "", "Lcom/zara/assistant/domain/model/SystemAction;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "invoke", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/AIResponse;", "command", "Lcom/zara/assistant/domain/model/VoiceCommand;", "invoke-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class ProcessVoiceCommandUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.VoiceRepository voiceRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.AIRepository aiRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.ConversationRepository conversationRepository = null;
    
    @javax.inject.Inject()
    public ProcessVoiceCommandUseCase(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.VoiceRepository voiceRepository, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.AIRepository aiRepository, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.ConversationRepository conversationRepository) {
        super();
    }
    
    private final java.lang.Object executeSystemActions(java.util.List<com.zara.assistant.domain.model.SystemAction> actions, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}