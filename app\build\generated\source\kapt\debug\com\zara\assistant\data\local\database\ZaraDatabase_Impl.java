package com.zara.assistant.data.local.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.zara.assistant.data.local.dao.CommandDao;
import com.zara.assistant.data.local.dao.CommandDao_Impl;
import com.zara.assistant.data.local.dao.ConversationDao;
import com.zara.assistant.data.local.dao.ConversationDao_Impl;
import com.zara.assistant.data.local.dao.SettingsDao;
import com.zara.assistant.data.local.dao.SettingsDao_Impl;
import com.zara.assistant.data.local.dao.UserLearningDao;
import com.zara.assistant.data.local.dao.UserLearningDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class ZaraDatabase_Impl extends ZaraDatabase {
  private volatile ConversationDao _conversationDao;

  private volatile SettingsDao _settingsDao;

  private volatile CommandDao _commandDao;

  private volatile UserLearningDao _userLearningDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(2) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `conversations` (`id` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER, `isActive` INTEGER NOT NULL, `summary` TEXT, `totalMessages` INTEGER NOT NULL, `averageResponseTime` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `conversation_messages` (`id` TEXT NOT NULL, `conversationId` TEXT NOT NULL, `content` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `sender` TEXT NOT NULL, `messageType` TEXT NOT NULL, `metadata` TEXT NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`conversationId`) REFERENCES `conversations`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE TABLE IF NOT EXISTS `settings` (`key` TEXT NOT NULL, `value` TEXT NOT NULL, `type` TEXT NOT NULL, PRIMARY KEY(`key`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `voice_commands` (`id` TEXT NOT NULL, `text` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `confidence` REAL NOT NULL, `language` TEXT NOT NULL, `isProcessed` INTEGER NOT NULL, `processingTime` INTEGER NOT NULL, `commandType` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_profile` (`id` TEXT NOT NULL, `name` TEXT, `nickname` TEXT, `age` INTEGER, `occupation` TEXT, `location` TEXT, `timezone` TEXT, `createdAt` INTEGER NOT NULL, `lastUpdated` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_interactions` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `command` TEXT NOT NULL, `response` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `timeOfDay` INTEGER NOT NULL, `dayOfWeek` INTEGER NOT NULL, `success` INTEGER NOT NULL, `executionTimeMs` INTEGER NOT NULL, `contextData` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_patterns` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `patternType` TEXT NOT NULL, `description` TEXT NOT NULL, `confidence` REAL NOT NULL, `frequency` INTEGER NOT NULL, `lastSeen` INTEGER NOT NULL, `isActive` INTEGER NOT NULL, `metadata` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_preferences` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `preferenceKey` TEXT NOT NULL, `preferenceValue` TEXT NOT NULL, `confidence` REAL NOT NULL, `source` TEXT NOT NULL, `lastUpdated` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `conversation_history` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `sessionId` TEXT NOT NULL, `userInput` TEXT NOT NULL, `zaraResponse` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `conversationType` TEXT NOT NULL, `success` INTEGER NOT NULL, `contextData` TEXT, `sentiment` TEXT, `topics` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `search_cache` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `query` TEXT NOT NULL, `results` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `source` TEXT NOT NULL, `accessCount` INTEGER NOT NULL, `lastAccessed` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_favorites` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `category` TEXT NOT NULL, `item` TEXT NOT NULL, `confidence` REAL NOT NULL, `source` TEXT NOT NULL, `createdAt` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `behavioral_patterns` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `patternType` TEXT NOT NULL, `description` TEXT NOT NULL, `confidence` REAL NOT NULL, `frequency` INTEGER NOT NULL, `timePattern` TEXT, `contextPattern` TEXT, `lastSeen` INTEGER NOT NULL, `isActive` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `ml_model_data` (`modelName` TEXT NOT NULL, `modelVersion` TEXT NOT NULL, `trainingData` TEXT NOT NULL, `accuracy` REAL NOT NULL, `lastTrained` INTEGER NOT NULL, `isActive` INTEGER NOT NULL, PRIMARY KEY(`modelName`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `contextual_data` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `timestamp` INTEGER NOT NULL, `batteryLevel` INTEGER, `isCharging` INTEGER, `wifiConnected` INTEGER, `bluetoothConnected` INTEGER, `foregroundApp` TEXT, `location` TEXT, `weatherCondition` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '3af5e5eb76049bd1b7378cb255b19762')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `conversations`");
        db.execSQL("DROP TABLE IF EXISTS `conversation_messages`");
        db.execSQL("DROP TABLE IF EXISTS `settings`");
        db.execSQL("DROP TABLE IF EXISTS `voice_commands`");
        db.execSQL("DROP TABLE IF EXISTS `user_profile`");
        db.execSQL("DROP TABLE IF EXISTS `user_interactions`");
        db.execSQL("DROP TABLE IF EXISTS `user_patterns`");
        db.execSQL("DROP TABLE IF EXISTS `user_preferences`");
        db.execSQL("DROP TABLE IF EXISTS `conversation_history`");
        db.execSQL("DROP TABLE IF EXISTS `search_cache`");
        db.execSQL("DROP TABLE IF EXISTS `user_favorites`");
        db.execSQL("DROP TABLE IF EXISTS `behavioral_patterns`");
        db.execSQL("DROP TABLE IF EXISTS `ml_model_data`");
        db.execSQL("DROP TABLE IF EXISTS `contextual_data`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsConversations = new HashMap<String, TableInfo.Column>(7);
        _columnsConversations.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("endTime", new TableInfo.Column("endTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("summary", new TableInfo.Column("summary", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("totalMessages", new TableInfo.Column("totalMessages", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversations.put("averageResponseTime", new TableInfo.Column("averageResponseTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysConversations = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesConversations = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoConversations = new TableInfo("conversations", _columnsConversations, _foreignKeysConversations, _indicesConversations);
        final TableInfo _existingConversations = TableInfo.read(db, "conversations");
        if (!_infoConversations.equals(_existingConversations)) {
          return new RoomOpenHelper.ValidationResult(false, "conversations(com.zara.assistant.data.local.database.entities.ConversationEntity).\n"
                  + " Expected:\n" + _infoConversations + "\n"
                  + " Found:\n" + _existingConversations);
        }
        final HashMap<String, TableInfo.Column> _columnsConversationMessages = new HashMap<String, TableInfo.Column>(7);
        _columnsConversationMessages.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationMessages.put("conversationId", new TableInfo.Column("conversationId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationMessages.put("content", new TableInfo.Column("content", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationMessages.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationMessages.put("sender", new TableInfo.Column("sender", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationMessages.put("messageType", new TableInfo.Column("messageType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationMessages.put("metadata", new TableInfo.Column("metadata", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysConversationMessages = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysConversationMessages.add(new TableInfo.ForeignKey("conversations", "CASCADE", "NO ACTION", Arrays.asList("conversationId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesConversationMessages = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoConversationMessages = new TableInfo("conversation_messages", _columnsConversationMessages, _foreignKeysConversationMessages, _indicesConversationMessages);
        final TableInfo _existingConversationMessages = TableInfo.read(db, "conversation_messages");
        if (!_infoConversationMessages.equals(_existingConversationMessages)) {
          return new RoomOpenHelper.ValidationResult(false, "conversation_messages(com.zara.assistant.data.local.database.entities.ConversationMessageEntity).\n"
                  + " Expected:\n" + _infoConversationMessages + "\n"
                  + " Found:\n" + _existingConversationMessages);
        }
        final HashMap<String, TableInfo.Column> _columnsSettings = new HashMap<String, TableInfo.Column>(3);
        _columnsSettings.put("key", new TableInfo.Column("key", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSettings.put("value", new TableInfo.Column("value", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSettings.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSettings = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSettings = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSettings = new TableInfo("settings", _columnsSettings, _foreignKeysSettings, _indicesSettings);
        final TableInfo _existingSettings = TableInfo.read(db, "settings");
        if (!_infoSettings.equals(_existingSettings)) {
          return new RoomOpenHelper.ValidationResult(false, "settings(com.zara.assistant.data.local.database.entities.SettingsEntity).\n"
                  + " Expected:\n" + _infoSettings + "\n"
                  + " Found:\n" + _existingSettings);
        }
        final HashMap<String, TableInfo.Column> _columnsVoiceCommands = new HashMap<String, TableInfo.Column>(8);
        _columnsVoiceCommands.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("text", new TableInfo.Column("text", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("language", new TableInfo.Column("language", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("isProcessed", new TableInfo.Column("isProcessed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("processingTime", new TableInfo.Column("processingTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("commandType", new TableInfo.Column("commandType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysVoiceCommands = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesVoiceCommands = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoVoiceCommands = new TableInfo("voice_commands", _columnsVoiceCommands, _foreignKeysVoiceCommands, _indicesVoiceCommands);
        final TableInfo _existingVoiceCommands = TableInfo.read(db, "voice_commands");
        if (!_infoVoiceCommands.equals(_existingVoiceCommands)) {
          return new RoomOpenHelper.ValidationResult(false, "voice_commands(com.zara.assistant.data.local.database.entities.VoiceCommandEntity).\n"
                  + " Expected:\n" + _infoVoiceCommands + "\n"
                  + " Found:\n" + _existingVoiceCommands);
        }
        final HashMap<String, TableInfo.Column> _columnsUserProfile = new HashMap<String, TableInfo.Column>(9);
        _columnsUserProfile.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("name", new TableInfo.Column("name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("nickname", new TableInfo.Column("nickname", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("age", new TableInfo.Column("age", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("occupation", new TableInfo.Column("occupation", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("timezone", new TableInfo.Column("timezone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserProfile.put("lastUpdated", new TableInfo.Column("lastUpdated", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserProfile = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserProfile = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserProfile = new TableInfo("user_profile", _columnsUserProfile, _foreignKeysUserProfile, _indicesUserProfile);
        final TableInfo _existingUserProfile = TableInfo.read(db, "user_profile");
        if (!_infoUserProfile.equals(_existingUserProfile)) {
          return new RoomOpenHelper.ValidationResult(false, "user_profile(com.zara.assistant.domain.model.UserProfile).\n"
                  + " Expected:\n" + _infoUserProfile + "\n"
                  + " Found:\n" + _existingUserProfile);
        }
        final HashMap<String, TableInfo.Column> _columnsUserInteractions = new HashMap<String, TableInfo.Column>(9);
        _columnsUserInteractions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("command", new TableInfo.Column("command", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("response", new TableInfo.Column("response", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("timeOfDay", new TableInfo.Column("timeOfDay", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("dayOfWeek", new TableInfo.Column("dayOfWeek", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("success", new TableInfo.Column("success", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("executionTimeMs", new TableInfo.Column("executionTimeMs", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserInteractions.put("contextData", new TableInfo.Column("contextData", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserInteractions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserInteractions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserInteractions = new TableInfo("user_interactions", _columnsUserInteractions, _foreignKeysUserInteractions, _indicesUserInteractions);
        final TableInfo _existingUserInteractions = TableInfo.read(db, "user_interactions");
        if (!_infoUserInteractions.equals(_existingUserInteractions)) {
          return new RoomOpenHelper.ValidationResult(false, "user_interactions(com.zara.assistant.domain.model.UserInteraction).\n"
                  + " Expected:\n" + _infoUserInteractions + "\n"
                  + " Found:\n" + _existingUserInteractions);
        }
        final HashMap<String, TableInfo.Column> _columnsUserPatterns = new HashMap<String, TableInfo.Column>(8);
        _columnsUserPatterns.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("patternType", new TableInfo.Column("patternType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("frequency", new TableInfo.Column("frequency", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("lastSeen", new TableInfo.Column("lastSeen", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPatterns.put("metadata", new TableInfo.Column("metadata", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserPatterns = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserPatterns = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserPatterns = new TableInfo("user_patterns", _columnsUserPatterns, _foreignKeysUserPatterns, _indicesUserPatterns);
        final TableInfo _existingUserPatterns = TableInfo.read(db, "user_patterns");
        if (!_infoUserPatterns.equals(_existingUserPatterns)) {
          return new RoomOpenHelper.ValidationResult(false, "user_patterns(com.zara.assistant.domain.model.UserPattern).\n"
                  + " Expected:\n" + _infoUserPatterns + "\n"
                  + " Found:\n" + _existingUserPatterns);
        }
        final HashMap<String, TableInfo.Column> _columnsUserPreferences = new HashMap<String, TableInfo.Column>(6);
        _columnsUserPreferences.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("preferenceKey", new TableInfo.Column("preferenceKey", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("preferenceValue", new TableInfo.Column("preferenceValue", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("source", new TableInfo.Column("source", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("lastUpdated", new TableInfo.Column("lastUpdated", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserPreferences = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserPreferences = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserPreferences = new TableInfo("user_preferences", _columnsUserPreferences, _foreignKeysUserPreferences, _indicesUserPreferences);
        final TableInfo _existingUserPreferences = TableInfo.read(db, "user_preferences");
        if (!_infoUserPreferences.equals(_existingUserPreferences)) {
          return new RoomOpenHelper.ValidationResult(false, "user_preferences(com.zara.assistant.domain.model.UserPreference).\n"
                  + " Expected:\n" + _infoUserPreferences + "\n"
                  + " Found:\n" + _existingUserPreferences);
        }
        final HashMap<String, TableInfo.Column> _columnsConversationHistory = new HashMap<String, TableInfo.Column>(10);
        _columnsConversationHistory.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("sessionId", new TableInfo.Column("sessionId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("userInput", new TableInfo.Column("userInput", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("zaraResponse", new TableInfo.Column("zaraResponse", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("conversationType", new TableInfo.Column("conversationType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("success", new TableInfo.Column("success", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("contextData", new TableInfo.Column("contextData", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("sentiment", new TableInfo.Column("sentiment", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsConversationHistory.put("topics", new TableInfo.Column("topics", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysConversationHistory = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesConversationHistory = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoConversationHistory = new TableInfo("conversation_history", _columnsConversationHistory, _foreignKeysConversationHistory, _indicesConversationHistory);
        final TableInfo _existingConversationHistory = TableInfo.read(db, "conversation_history");
        if (!_infoConversationHistory.equals(_existingConversationHistory)) {
          return new RoomOpenHelper.ValidationResult(false, "conversation_history(com.zara.assistant.domain.model.ConversationHistory).\n"
                  + " Expected:\n" + _infoConversationHistory + "\n"
                  + " Found:\n" + _existingConversationHistory);
        }
        final HashMap<String, TableInfo.Column> _columnsSearchCache = new HashMap<String, TableInfo.Column>(7);
        _columnsSearchCache.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSearchCache.put("query", new TableInfo.Column("query", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSearchCache.put("results", new TableInfo.Column("results", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSearchCache.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSearchCache.put("source", new TableInfo.Column("source", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSearchCache.put("accessCount", new TableInfo.Column("accessCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSearchCache.put("lastAccessed", new TableInfo.Column("lastAccessed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSearchCache = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSearchCache = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSearchCache = new TableInfo("search_cache", _columnsSearchCache, _foreignKeysSearchCache, _indicesSearchCache);
        final TableInfo _existingSearchCache = TableInfo.read(db, "search_cache");
        if (!_infoSearchCache.equals(_existingSearchCache)) {
          return new RoomOpenHelper.ValidationResult(false, "search_cache(com.zara.assistant.domain.model.SearchCache).\n"
                  + " Expected:\n" + _infoSearchCache + "\n"
                  + " Found:\n" + _existingSearchCache);
        }
        final HashMap<String, TableInfo.Column> _columnsUserFavorites = new HashMap<String, TableInfo.Column>(6);
        _columnsUserFavorites.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserFavorites.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserFavorites.put("item", new TableInfo.Column("item", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserFavorites.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserFavorites.put("source", new TableInfo.Column("source", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserFavorites.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserFavorites = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserFavorites = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserFavorites = new TableInfo("user_favorites", _columnsUserFavorites, _foreignKeysUserFavorites, _indicesUserFavorites);
        final TableInfo _existingUserFavorites = TableInfo.read(db, "user_favorites");
        if (!_infoUserFavorites.equals(_existingUserFavorites)) {
          return new RoomOpenHelper.ValidationResult(false, "user_favorites(com.zara.assistant.domain.model.UserFavorite).\n"
                  + " Expected:\n" + _infoUserFavorites + "\n"
                  + " Found:\n" + _existingUserFavorites);
        }
        final HashMap<String, TableInfo.Column> _columnsBehavioralPatterns = new HashMap<String, TableInfo.Column>(9);
        _columnsBehavioralPatterns.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("patternType", new TableInfo.Column("patternType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("frequency", new TableInfo.Column("frequency", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("timePattern", new TableInfo.Column("timePattern", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("contextPattern", new TableInfo.Column("contextPattern", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("lastSeen", new TableInfo.Column("lastSeen", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBehavioralPatterns.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysBehavioralPatterns = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesBehavioralPatterns = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoBehavioralPatterns = new TableInfo("behavioral_patterns", _columnsBehavioralPatterns, _foreignKeysBehavioralPatterns, _indicesBehavioralPatterns);
        final TableInfo _existingBehavioralPatterns = TableInfo.read(db, "behavioral_patterns");
        if (!_infoBehavioralPatterns.equals(_existingBehavioralPatterns)) {
          return new RoomOpenHelper.ValidationResult(false, "behavioral_patterns(com.zara.assistant.domain.model.BehavioralPattern).\n"
                  + " Expected:\n" + _infoBehavioralPatterns + "\n"
                  + " Found:\n" + _existingBehavioralPatterns);
        }
        final HashMap<String, TableInfo.Column> _columnsMlModelData = new HashMap<String, TableInfo.Column>(6);
        _columnsMlModelData.put("modelName", new TableInfo.Column("modelName", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMlModelData.put("modelVersion", new TableInfo.Column("modelVersion", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMlModelData.put("trainingData", new TableInfo.Column("trainingData", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMlModelData.put("accuracy", new TableInfo.Column("accuracy", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMlModelData.put("lastTrained", new TableInfo.Column("lastTrained", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMlModelData.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysMlModelData = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesMlModelData = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoMlModelData = new TableInfo("ml_model_data", _columnsMlModelData, _foreignKeysMlModelData, _indicesMlModelData);
        final TableInfo _existingMlModelData = TableInfo.read(db, "ml_model_data");
        if (!_infoMlModelData.equals(_existingMlModelData)) {
          return new RoomOpenHelper.ValidationResult(false, "ml_model_data(com.zara.assistant.domain.model.MLModelData).\n"
                  + " Expected:\n" + _infoMlModelData + "\n"
                  + " Found:\n" + _existingMlModelData);
        }
        final HashMap<String, TableInfo.Column> _columnsContextualData = new HashMap<String, TableInfo.Column>(9);
        _columnsContextualData.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("batteryLevel", new TableInfo.Column("batteryLevel", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("isCharging", new TableInfo.Column("isCharging", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("wifiConnected", new TableInfo.Column("wifiConnected", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("bluetoothConnected", new TableInfo.Column("bluetoothConnected", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("foregroundApp", new TableInfo.Column("foregroundApp", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsContextualData.put("weatherCondition", new TableInfo.Column("weatherCondition", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysContextualData = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesContextualData = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoContextualData = new TableInfo("contextual_data", _columnsContextualData, _foreignKeysContextualData, _indicesContextualData);
        final TableInfo _existingContextualData = TableInfo.read(db, "contextual_data");
        if (!_infoContextualData.equals(_existingContextualData)) {
          return new RoomOpenHelper.ValidationResult(false, "contextual_data(com.zara.assistant.domain.model.ContextualData).\n"
                  + " Expected:\n" + _infoContextualData + "\n"
                  + " Found:\n" + _existingContextualData);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "3af5e5eb76049bd1b7378cb255b19762", "43649feb4f29148814335f6b77d6d2a9");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "conversations","conversation_messages","settings","voice_commands","user_profile","user_interactions","user_patterns","user_preferences","conversation_history","search_cache","user_favorites","behavioral_patterns","ml_model_data","contextual_data");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `conversations`");
      _db.execSQL("DELETE FROM `conversation_messages`");
      _db.execSQL("DELETE FROM `settings`");
      _db.execSQL("DELETE FROM `voice_commands`");
      _db.execSQL("DELETE FROM `user_profile`");
      _db.execSQL("DELETE FROM `user_interactions`");
      _db.execSQL("DELETE FROM `user_patterns`");
      _db.execSQL("DELETE FROM `user_preferences`");
      _db.execSQL("DELETE FROM `conversation_history`");
      _db.execSQL("DELETE FROM `search_cache`");
      _db.execSQL("DELETE FROM `user_favorites`");
      _db.execSQL("DELETE FROM `behavioral_patterns`");
      _db.execSQL("DELETE FROM `ml_model_data`");
      _db.execSQL("DELETE FROM `contextual_data`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(ConversationDao.class, ConversationDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SettingsDao.class, SettingsDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CommandDao.class, CommandDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserLearningDao.class, UserLearningDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public ConversationDao conversationDao() {
    if (_conversationDao != null) {
      return _conversationDao;
    } else {
      synchronized(this) {
        if(_conversationDao == null) {
          _conversationDao = new ConversationDao_Impl(this);
        }
        return _conversationDao;
      }
    }
  }

  @Override
  public SettingsDao settingsDao() {
    if (_settingsDao != null) {
      return _settingsDao;
    } else {
      synchronized(this) {
        if(_settingsDao == null) {
          _settingsDao = new SettingsDao_Impl(this);
        }
        return _settingsDao;
      }
    }
  }

  @Override
  public CommandDao commandDao() {
    if (_commandDao != null) {
      return _commandDao;
    } else {
      synchronized(this) {
        if(_commandDao == null) {
          _commandDao = new CommandDao_Impl(this);
        }
        return _commandDao;
      }
    }
  }

  @Override
  public UserLearningDao userLearningDao() {
    if (_userLearningDao != null) {
      return _userLearningDao;
    } else {
      synchronized(this) {
        if(_userLearningDao == null) {
          _userLearningDao = new UserLearningDao_Impl(this);
        }
        return _userLearningDao;
      }
    }
  }
}
