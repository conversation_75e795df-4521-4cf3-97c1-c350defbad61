package com.zara.assistant.domain.model;

/**
 * Types of conversations/intents
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/domain/model/ConversationType;", "", "(Ljava/lang/String;I)V", "INFORMATION_REQUEST", "MUSIC_CONTROL", "MESSAGING", "APP_CONTROL", "SYSTEM_CONTROL", "EXTENDED_CHAT", "GENERAL_CHAT", "EXIT_COMMAND", "UNKNOWN", "app_debug"})
public enum ConversationType {
    /*public static final*/ INFORMATION_REQUEST /* = new INFORMATION_REQUEST() */,
    /*public static final*/ MUSIC_CONTROL /* = new MUSIC_CONTROL() */,
    /*public static final*/ MESSAGING /* = new MESSAGING() */,
    /*public static final*/ APP_CONTROL /* = new APP_CONTROL() */,
    /*public static final*/ SYSTEM_CONTROL /* = new SYSTEM_CONTROL() */,
    /*public static final*/ EXTENDED_CHAT /* = new EXTENDED_CHAT() */,
    /*public static final*/ GENERAL_CHAT /* = new GENERAL_CHAT() */,
    /*public static final*/ EXIT_COMMAND /* = new EXIT_COMMAND() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    ConversationType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.ConversationType> getEntries() {
        return null;
    }
}