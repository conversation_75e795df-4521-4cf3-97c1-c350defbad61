package com.zara.assistant.core;

/**
 * Application-wide constants
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0011\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u000f\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0012"}, d2 = {"Lcom/zara/assistant/core/Constants;", "", "()V", "AI", "AIPersonality", "AIResponseStyle", "API", "Animation", "Database", "ErrorCodes", "IntentActions", "Permissions", "Preferences", "SystemCommands", "UI", "Voice", "VoiceState", "WakeWord", "app_debug"})
public final class Constants {
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.core.Constants INSTANCE = null;
    
    private Constants() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/core/Constants$AI;", "", "()V", "FREQUENCY_PENALTY", "", "MAX_CONVERSATION_HISTORY", "", "MAX_TOKENS", "PRESENCE_PENALTY", "RESPONSE_TIMEOUT", "", "TEMPERATURE", "TOP_P", "app_debug"})
    public static final class AI {
        public static final int MAX_TOKENS = 75;
        public static final float TEMPERATURE = 0.7F;
        public static final float TOP_P = 0.9F;
        public static final float FREQUENCY_PENALTY = 0.0F;
        public static final float PRESENCE_PENALTY = 0.0F;
        public static final int MAX_CONVERSATION_HISTORY = 10;
        public static final long RESPONSE_TIMEOUT = 15000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.AI INSTANCE = null;
        
        private AI() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$AIPersonality;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "PROFESSIONAL", "FRIENDLY", "CASUAL", "app_debug"})
    public static enum AIPersonality {
        /*public static final*/ PROFESSIONAL /* = new PROFESSIONAL(null) */,
        /*public static final*/ FRIENDLY /* = new FRIENDLY(null) */,
        /*public static final*/ CASUAL /* = new CASUAL(null) */;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String displayName = null;
        
        AIPersonality(java.lang.String displayName) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.zara.assistant.core.Constants.AIPersonality> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$AIResponseStyle;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "BRIEF", "DETAILED", "CONVERSATIONAL", "app_debug"})
    public static enum AIResponseStyle {
        /*public static final*/ BRIEF /* = new BRIEF(null) */,
        /*public static final*/ DETAILED /* = new DETAILED(null) */,
        /*public static final*/ CONVERSATIONAL /* = new CONVERSATIONAL(null) */;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String displayName = null;
        
        AIResponseStyle(java.lang.String displayName) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.zara.assistant.core.Constants.AIResponseStyle> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$API;", "", "()V", "COHERE_BASE_URL", "", "CONNECT_TIMEOUT", "", "PERPLEXITY_BASE_URL", "READ_TIMEOUT", "REQUEST_TIMEOUT", "app_debug"})
    public static final class API {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String COHERE_BASE_URL = "https://api.cohere.ai/";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERPLEXITY_BASE_URL = "https://api.perplexity.ai/";
        public static final long REQUEST_TIMEOUT = 30L;
        public static final long CONNECT_TIMEOUT = 15L;
        public static final long READ_TIMEOUT = 30L;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.API INSTANCE = null;
        
        private API() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$Animation;", "", "()V", "FADE_DURATION", "", "LONG_DURATION", "MEDIUM_DURATION", "SHORT_DURATION", "SLIDE_DURATION", "VOICE_RIPPLE_DURATION", "app_debug"})
    public static final class Animation {
        public static final long SHORT_DURATION = 150L;
        public static final long MEDIUM_DURATION = 300L;
        public static final long LONG_DURATION = 500L;
        public static final long VOICE_RIPPLE_DURATION = 1000L;
        public static final long FADE_DURATION = 200L;
        public static final long SLIDE_DURATION = 250L;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Animation INSTANCE = null;
        
        private Animation() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$Database;", "", "()V", "COMMANDS_TABLE", "", "CONVERSATION_TABLE", "DATABASE_NAME", "DATABASE_VERSION", "", "SETTINGS_TABLE", "app_debug"})
    public static final class Database {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DATABASE_NAME = "zara_database";
        public static final int DATABASE_VERSION = 1;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONVERSATION_TABLE = "conversations";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SETTINGS_TABLE = "settings";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String COMMANDS_TABLE = "commands";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Database INSTANCE = null;
        
        private Database() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/core/Constants$ErrorCodes;", "", "()V", "ACCESSIBILITY_SERVICE_ERROR", "", "AI_SERVICE_ERROR", "MICROPHONE_PERMISSION_DENIED", "NETWORK_ERROR", "SPEECH_RECOGNITION_ERROR", "TEXT_TO_SPEECH_ERROR", "UNKNOWN_ERROR", "WAKE_WORD_SERVICE_ERROR", "app_debug"})
    public static final class ErrorCodes {
        public static final int MICROPHONE_PERMISSION_DENIED = 1001;
        public static final int NETWORK_ERROR = 1002;
        public static final int AI_SERVICE_ERROR = 1003;
        public static final int SPEECH_RECOGNITION_ERROR = 1004;
        public static final int TEXT_TO_SPEECH_ERROR = 1005;
        public static final int ACCESSIBILITY_SERVICE_ERROR = 1006;
        public static final int WAKE_WORD_SERVICE_ERROR = 1007;
        public static final int UNKNOWN_ERROR = 9999;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.ErrorCodes INSTANCE = null;
        
        private ErrorCodes() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$IntentActions;", "", "()V", "PROCESS_COMMAND", "", "SPEAK_RESPONSE", "START_LISTENING", "STOP_LISTENING", "VOICE_COMMAND", "WAKE_WORD_DETECTED", "app_debug"})
    public static final class IntentActions {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_COMMAND = "com.zara.assistant.VOICE_COMMAND";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WAKE_WORD_DETECTED = "com.zara.assistant.WAKE_WORD_DETECTED";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String START_LISTENING = "com.zara.assistant.START_LISTENING";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String STOP_LISTENING = "com.zara.assistant.STOP_LISTENING";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PROCESS_COMMAND = "com.zara.assistant.PROCESS_COMMAND";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SPEAK_RESPONSE = "com.zara.assistant.SPEAK_RESPONSE";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.IntentActions INSTANCE = null;
        
        private IntentActions() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/zara/assistant/core/Constants$Permissions;", "", "()V", "ACCESS_COARSE_LOCATION", "", "ACCESS_FINE_LOCATION", "CALL_PHONE", "POST_NOTIFICATIONS", "READ_CONTACTS", "READ_EXTERNAL_STORAGE", "RECORD_AUDIO", "SEND_SMS", "SYSTEM_ALERT_WINDOW", "WRITE_EXTERNAL_STORAGE", "app_debug"})
    public static final class Permissions {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String RECORD_AUDIO = "android.permission.RECORD_AUDIO";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SYSTEM_ALERT_WINDOW = "android.permission.SYSTEM_ALERT_WINDOW";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CALL_PHONE = "android.permission.CALL_PHONE";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SEND_SMS = "android.permission.SEND_SMS";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String READ_CONTACTS = "android.permission.READ_CONTACTS";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACCESS_FINE_LOCATION = "android.permission.ACCESS_FINE_LOCATION";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACCESS_COARSE_LOCATION = "android.permission.ACCESS_COARSE_LOCATION";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String READ_EXTERNAL_STORAGE = "android.permission.READ_EXTERNAL_STORAGE";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WRITE_EXTERNAL_STORAGE = "android.permission.WRITE_EXTERNAL_STORAGE";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String POST_NOTIFICATIONS = "android.permission.POST_NOTIFICATIONS";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Permissions INSTANCE = null;
        
        private Permissions() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\r\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/zara/assistant/core/Constants$Preferences;", "", "()V", "ACCESSIBILITY_SERVICE_ENABLED", "", "AI_PERSONALITY", "AI_RESPONSE_STYLE", "ANALYTICS_ENABLED", "AUTO_LISTEN", "CONVERSATION_HISTORY_ENABLED", "FIRST_LAUNCH", "NOTIFICATION_ACCESS_ENABLED", "SPEECH_PITCH", "SPEECH_RATE", "VOICE_LANGUAGE", "WAKE_WORD_ENABLED", "WAKE_WORD_SENSITIVITY", "app_debug"})
    public static final class Preferences {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WAKE_WORD_ENABLED = "wake_word_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WAKE_WORD_SENSITIVITY = "wake_word_sensitivity";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SPEECH_RATE = "speech_rate";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SPEECH_PITCH = "speech_pitch";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AI_PERSONALITY = "ai_personality";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AI_RESPONSE_STYLE = "ai_response_style";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VOICE_LANGUAGE = "voice_language";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AUTO_LISTEN = "auto_listen";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CONVERSATION_HISTORY_ENABLED = "conversation_history_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ANALYTICS_ENABLED = "analytics_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String FIRST_LAUNCH = "first_launch";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ACCESSIBILITY_SERVICE_ENABLED = "accessibility_service_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String NOTIFICATION_ACCESS_ENABLED = "notification_access_enabled";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Preferences INSTANCE = null;
        
        private Preferences() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0013\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/zara/assistant/core/Constants$SystemCommands;", "", "()V", "CALL_CONTACT", "", "CLOSE_APP", "DECREASE_VOLUME", "DISABLE_AIRPLANE_MODE", "ENABLE_AIRPLANE_MODE", "INCREASE_VOLUME", "NEXT_SONG", "OPEN_APP", "PAUSE_MUSIC", "PLAY_MUSIC", "PREVIOUS_SONG", "READ_NOTIFICATIONS", "SEND_MESSAGE", "SET_BRIGHTNESS", "SWITCH_APP", "TURN_OFF_BLUETOOTH", "TURN_OFF_WIFI", "TURN_ON_BLUETOOTH", "TURN_ON_WIFI", "app_debug"})
    public static final class SystemCommands {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String OPEN_APP = "open";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CLOSE_APP = "close";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SWITCH_APP = "switch";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TURN_ON_WIFI = "turn_on_wifi";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TURN_OFF_WIFI = "turn_off_wifi";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TURN_ON_BLUETOOTH = "turn_on_bluetooth";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String TURN_OFF_BLUETOOTH = "turn_off_bluetooth";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String INCREASE_VOLUME = "increase_volume";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DECREASE_VOLUME = "decrease_volume";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SET_BRIGHTNESS = "set_brightness";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ENABLE_AIRPLANE_MODE = "enable_airplane_mode";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DISABLE_AIRPLANE_MODE = "disable_airplane_mode";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String CALL_CONTACT = "call_contact";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SEND_MESSAGE = "send_message";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String READ_NOTIFICATIONS = "read_notifications";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PLAY_MUSIC = "play_music";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PAUSE_MUSIC = "pause_music";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String NEXT_SONG = "next_song";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PREVIOUS_SONG = "previous_song";
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.SystemCommands INSTANCE = null;
        
        private SystemCommands() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/zara/assistant/core/Constants$UI;", "", "()V", "BUTTON_CORNER_RADIUS_DP", "", "CARD_CORNER_RADIUS_DP", "ELEVATION_DP", "MARGIN_DP", "PADDING_DP", "VOICE_BUTTON_SIZE_DP", "app_debug"})
    public static final class UI {
        public static final int VOICE_BUTTON_SIZE_DP = 120;
        public static final int CARD_CORNER_RADIUS_DP = 20;
        public static final int BUTTON_CORNER_RADIUS_DP = 16;
        public static final int ELEVATION_DP = 8;
        public static final int MARGIN_DP = 16;
        public static final int PADDING_DP = 16;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.UI INSTANCE = null;
        
        private UI() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/core/Constants$Voice;", "", "()V", "DEFAULT_PITCH", "", "DEFAULT_SPEECH_RATE", "LISTENING_TIMEOUT", "", "MAX_PITCH", "MAX_SPEECH_RATE", "MIN_PITCH", "MIN_SPEECH_RATE", "RECOGNITION_TIMEOUT", "app_debug"})
    public static final class Voice {
        public static final float DEFAULT_SPEECH_RATE = 1.0F;
        public static final float DEFAULT_PITCH = 1.0F;
        public static final float MIN_SPEECH_RATE = 0.5F;
        public static final float MAX_SPEECH_RATE = 2.0F;
        public static final float MIN_PITCH = 0.5F;
        public static final float MAX_PITCH = 2.0F;
        public static final long RECOGNITION_TIMEOUT = 5000L;
        public static final long LISTENING_TIMEOUT = 10000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.Voice INSTANCE = null;
        
        private Voice() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/zara/assistant/core/Constants$VoiceState;", "", "(Ljava/lang/String;I)V", "IDLE", "LISTENING_WAKE_WORD", "LISTENING_COMMAND", "PROCESSING", "SPEAKING", "ERROR", "app_debug"})
    public static enum VoiceState {
        /*public static final*/ IDLE /* = new IDLE() */,
        /*public static final*/ LISTENING_WAKE_WORD /* = new LISTENING_WAKE_WORD() */,
        /*public static final*/ LISTENING_COMMAND /* = new LISTENING_COMMAND() */,
        /*public static final*/ PROCESSING /* = new PROCESSING() */,
        /*public static final*/ SPEAKING /* = new SPEAKING() */,
        /*public static final*/ ERROR /* = new ERROR() */;
        
        VoiceState() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.zara.assistant.core.Constants.VoiceState> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/core/Constants$WakeWord;", "", "()V", "ALTERNATIVE_KEYWORD", "", "AUDIO_SAMPLE_RATE", "", "DEFAULT_SENSITIVITY", "", "FRAME_LENGTH", "KEYWORD", "MAX_SENSITIVITY", "MIN_SENSITIVITY", "app_debug"})
    public static final class WakeWord {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String KEYWORD = "Hey Zara";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ALTERNATIVE_KEYWORD = "Zara";
        public static final float DEFAULT_SENSITIVITY = 0.5F;
        public static final float MIN_SENSITIVITY = 0.1F;
        public static final float MAX_SENSITIVITY = 1.0F;
        public static final int AUDIO_SAMPLE_RATE = 16000;
        public static final int FRAME_LENGTH = 512;
        @org.jetbrains.annotations.NotNull()
        public static final com.zara.assistant.core.Constants.WakeWord INSTANCE = null;
        
        private WakeWord() {
            super();
        }
    }
}