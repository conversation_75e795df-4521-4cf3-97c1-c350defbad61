// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import com.zara.assistant.data.local.dao.CommandDao;
import com.zara.assistant.data.local.database.ZaraDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideCommandDaoFactory implements Factory<CommandDao> {
  private final Provider<ZaraDatabase> databaseProvider;

  public DatabaseModule_ProvideCommandDaoFactory(Provider<ZaraDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CommandDao get() {
    return provideCommandDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideCommandDaoFactory create(
      Provider<ZaraDatabase> databaseProvider) {
    return new DatabaseModule_ProvideCommandDaoFactory(databaseProvider);
  }

  public static CommandDao provideCommandDao(ZaraDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideCommandDao(database));
  }
}
