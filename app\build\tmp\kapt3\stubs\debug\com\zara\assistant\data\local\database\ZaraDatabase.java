package com.zara.assistant.data.local.database;

/**
 * Room database for Zara app
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u000b2\u00020\u0001:\u0001\u000bB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/data/local/database/ZaraDatabase;", "Landroidx/room/RoomDatabase;", "()V", "commandDao", "Lcom/zara/assistant/data/local/dao/CommandDao;", "conversationDao", "Lcom/zara/assistant/data/local/dao/ConversationDao;", "settingsDao", "Lcom/zara/assistant/data/local/dao/SettingsDao;", "userLearningDao", "Lcom/zara/assistant/data/local/dao/UserLearningDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.zara.assistant.data.local.database.entities.ConversationEntity.class, com.zara.assistant.data.local.database.entities.ConversationMessageEntity.class, com.zara.assistant.data.local.database.entities.SettingsEntity.class, com.zara.assistant.data.local.database.entities.VoiceCommandEntity.class, com.zara.assistant.domain.model.UserProfile.class, com.zara.assistant.domain.model.UserInteraction.class, com.zara.assistant.domain.model.UserPattern.class, com.zara.assistant.domain.model.UserPreference.class, com.zara.assistant.domain.model.ConversationHistory.class, com.zara.assistant.domain.model.SearchCache.class, com.zara.assistant.domain.model.UserFavorite.class, com.zara.assistant.domain.model.BehavioralPattern.class, com.zara.assistant.domain.model.MLModelData.class, com.zara.assistant.domain.model.ContextualData.class}, version = 2, exportSchema = false)
@androidx.room.TypeConverters(value = {com.zara.assistant.data.local.database.Converters.class})
public abstract class ZaraDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.zara.assistant.data.local.database.ZaraDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.data.local.database.ZaraDatabase.Companion Companion = null;
    
    public ZaraDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.zara.assistant.data.local.dao.ConversationDao conversationDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.zara.assistant.data.local.dao.SettingsDao settingsDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.zara.assistant.data.local.dao.CommandDao commandDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.zara.assistant.data.local.dao.UserLearningDao userLearningDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/zara/assistant/data/local/database/ZaraDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/zara/assistant/data/local/database/ZaraDatabase;", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.zara.assistant.data.local.database.ZaraDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}