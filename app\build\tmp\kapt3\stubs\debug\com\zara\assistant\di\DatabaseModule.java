package com.zara.assistant.di;

/**
 * Dependency injection module for database-related dependencies
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\r\u001a\u00020\u00062\b\b\u0001\u0010\u000e\u001a\u00020\u000fH\u0007\u00a8\u0006\u0010"}, d2 = {"Lcom/zara/assistant/di/DatabaseModule;", "", "()V", "provideCommandDao", "Lcom/zara/assistant/data/local/dao/CommandDao;", "database", "Lcom/zara/assistant/data/local/database/ZaraDatabase;", "provideConversationDao", "Lcom/zara/assistant/data/local/dao/ConversationDao;", "provideSettingsDao", "Lcom/zara/assistant/data/local/dao/SettingsDao;", "provideUserLearningDao", "Lcom/zara/assistant/data/local/dao/UserLearningDao;", "provideZaraDatabase", "context", "Landroid/content/Context;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DatabaseModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.di.DatabaseModule INSTANCE = null;
    
    private DatabaseModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.database.ZaraDatabase provideZaraDatabase(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.dao.ConversationDao provideConversationDao(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.database.ZaraDatabase database) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.dao.SettingsDao provideSettingsDao(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.database.ZaraDatabase database) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.dao.CommandDao provideCommandDao(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.database.ZaraDatabase database) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.local.dao.UserLearningDao provideUserLearningDao(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.database.ZaraDatabase database) {
        return null;
    }
}