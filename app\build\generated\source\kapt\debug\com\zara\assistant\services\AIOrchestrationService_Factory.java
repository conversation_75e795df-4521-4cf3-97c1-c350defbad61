// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AIOrchestrationService_Factory implements Factory<AIOrchestrationService> {
  private final Provider<Context> contextProvider;

  private final Provider<PersonalMemoryService> personalMemoryServiceProvider;

  private final Provider<MLPersonalizationService> mlPersonalizationServiceProvider;

  private final Provider<WebSearchService> webSearchServiceProvider;

  public AIOrchestrationService_Factory(Provider<Context> contextProvider,
      Provider<PersonalMemoryService> personalMemoryServiceProvider,
      Provider<MLPersonalizationService> mlPersonalizationServiceProvider,
      Provider<WebSearchService> webSearchServiceProvider) {
    this.contextProvider = contextProvider;
    this.personalMemoryServiceProvider = personalMemoryServiceProvider;
    this.mlPersonalizationServiceProvider = mlPersonalizationServiceProvider;
    this.webSearchServiceProvider = webSearchServiceProvider;
  }

  @Override
  public AIOrchestrationService get() {
    return newInstance(contextProvider.get(), personalMemoryServiceProvider.get(), mlPersonalizationServiceProvider.get(), webSearchServiceProvider.get());
  }

  public static AIOrchestrationService_Factory create(Provider<Context> contextProvider,
      Provider<PersonalMemoryService> personalMemoryServiceProvider,
      Provider<MLPersonalizationService> mlPersonalizationServiceProvider,
      Provider<WebSearchService> webSearchServiceProvider) {
    return new AIOrchestrationService_Factory(contextProvider, personalMemoryServiceProvider, mlPersonalizationServiceProvider, webSearchServiceProvider);
  }

  public static AIOrchestrationService newInstance(Context context,
      PersonalMemoryService personalMemoryService,
      MLPersonalizationService mlPersonalizationService, WebSearchService webSearchService) {
    return new AIOrchestrationService(context, personalMemoryService, mlPersonalizationService, webSearchService);
  }
}
