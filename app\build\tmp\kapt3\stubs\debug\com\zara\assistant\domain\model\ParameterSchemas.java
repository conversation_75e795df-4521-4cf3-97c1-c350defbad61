package com.zara.assistant.domain.model;

/**
 * Parameter schemas for different conversation types
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000f\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0006R\u0011\u0010\t\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0006R\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0006R\u0011\u0010\r\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0006R\u0011\u0010\u000f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0006R\u0011\u0010\u0011\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0006\u00a8\u0006\u0013"}, d2 = {"Lcom/zara/assistant/domain/model/ParameterSchemas;", "", "()V", "APP_CONTROL", "Lcom/zara/assistant/domain/model/CommandParameters;", "getAPP_CONTROL", "()Lcom/zara/assistant/domain/model/CommandParameters;", "EXTENDED_CHAT", "getEXTENDED_CHAT", "GENERAL_CHAT", "getGENERAL_CHAT", "INFORMATION_REQUEST", "getINFORMATION_REQUEST", "MESSAGING", "getMESSAGING", "MUSIC_CONTROL", "getMUSIC_CONTROL", "SYSTEM_CONTROL", "getSYSTEM_CONTROL", "app_debug"})
public final class ParameterSchemas {
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters INFORMATION_REQUEST = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters MUSIC_CONTROL = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters MESSAGING = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters APP_CONTROL = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters SYSTEM_CONTROL = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters EXTENDED_CHAT = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.zara.assistant.domain.model.CommandParameters GENERAL_CHAT = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.domain.model.ParameterSchemas INSTANCE = null;
    
    private ParameterSchemas() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getINFORMATION_REQUEST() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getMUSIC_CONTROL() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getMESSAGING() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getAPP_CONTROL() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getSYSTEM_CONTROL() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getEXTENDED_CHAT() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getGENERAL_CHAT() {
        return null;
    }
}