package com.zara.assistant.domain.repository;

/**
 * Repository interface for settings management
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b/\bf\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\b\u0010\u0004J\u000e\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\fH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u000eH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00070\u000eH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0010\u001a\u00020\u0011H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0012\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0013\u001a\u00020\u0014H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0015\u001a\u00020\u0016H\u00a6@\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010\u0019\u001a\u00020\u0007H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u001bJ\u000e\u0010\u001c\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u001d\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u001f\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\u001bJ\u000e\u0010 \u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010!\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\"\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010#\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010$\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00140&H&J\u001c\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00180\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b(\u0010\u0004J$\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010*\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b+\u0010,J$\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010.\u001a\u00020\fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b/\u00100J$\u00101\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b3\u00104J$\u00105\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u00104J,\u00107\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b8\u00109J$\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b;\u00104J\u001c\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00180\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b=\u0010\u0004J$\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u00104J$\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bA\u00104J$\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010C\u001a\u00020\u0011H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bD\u0010EJ$\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bG\u00104J$\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010I\u001a\u00020\u0007H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bJ\u0010\u001bJ$\u0010K\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00102\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bL\u00104J$\u0010M\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010N\u001a\u00020\u0016H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bO\u0010PJ$\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010R\u001a\u00020\u0014H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bS\u0010T\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006U"}, d2 = {"Lcom/zara/assistant/domain/repository/SettingsRepository;", "", "areNotificationsEnabled", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportSettings", "Lkotlin/Result;", "", "exportSettings-IoAF18A", "getAIPersonality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "getAIResponseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "getNotificationChannels", "", "getSupportedLanguages", "getThemeMode", "Lcom/zara/assistant/domain/repository/ThemeMode;", "getVoiceLanguage", "getVoiceSettings", "Lcom/zara/assistant/domain/model/VoiceSettings;", "getWakeWordSensitivity", "", "importSettings", "", "data", "importSettings-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isAccessibilityServiceEnabled", "isAnalyticsEnabled", "isChannelEnabled", "channelId", "isConversationHistoryEnabled", "isFirstLaunch", "isNotificationAccessEnabled", "isVoiceDataStorageEnabled", "isWakeWordEnabled", "observeVoiceSettings", "Lkotlinx/coroutines/flow/Flow;", "resetToDefaults", "resetToDefaults-IoAF18A", "setAIPersonality", "personality", "setAIPersonality-gIAlu-s", "(Lcom/zara/assistant/core/Constants$AIPersonality;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAIResponseStyle", "style", "setAIResponseStyle-gIAlu-s", "(Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAccessibilityServiceEnabled", "enabled", "setAccessibilityServiceEnabled-gIAlu-s", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAnalyticsEnabled", "setAnalyticsEnabled-gIAlu-s", "setChannelEnabled", "setChannelEnabled-0E7RQCE", "(Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setConversationHistoryEnabled", "setConversationHistoryEnabled-gIAlu-s", "setFirstLaunchCompleted", "setFirstLaunchCompleted-IoAF18A", "setNotificationAccessEnabled", "setNotificationAccessEnabled-gIAlu-s", "setNotificationsEnabled", "setNotificationsEnabled-gIAlu-s", "setThemeMode", "mode", "setThemeMode-gIAlu-s", "(Lcom/zara/assistant/domain/repository/ThemeMode;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setVoiceDataStorageEnabled", "setVoiceDataStorageEnabled-gIAlu-s", "setVoiceLanguage", "language", "setVoiceLanguage-gIAlu-s", "setWakeWordEnabled", "setWakeWordEnabled-gIAlu-s", "setWakeWordSensitivity", "sensitivity", "setWakeWordSensitivity-gIAlu-s", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVoiceSettings", "settings", "updateVoiceSettings-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface SettingsRepository {
    
    /**
     * Voice settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getVoiceSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.VoiceSettings> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.VoiceSettings> observeVoiceSettings();
    
    /**
     * Wake word settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isWakeWordEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWakeWordSensitivity(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Float> $completion);
    
    /**
     * AI settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAIPersonality(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.core.Constants.AIPersonality> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAIResponseStyle(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.core.Constants.AIResponseStyle> $completion);
    
    /**
     * Privacy settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isConversationHistoryEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isAnalyticsEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isVoiceDataStorageEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * Accessibility settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isAccessibilityServiceEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isNotificationAccessEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * Language and localization
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getVoiceLanguage(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSupportedLanguages(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    /**
     * App preferences
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isFirstLaunch(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getThemeMode(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.repository.ThemeMode> $completion);
    
    /**
     * Notification settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object areNotificationsEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getNotificationChannels(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isChannelEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String channelId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
}