// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.app.NotificationManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WakeWordService_MembersInjector implements MembersInjector<WakeWordService> {
  private final Provider<NotificationManager> notificationManagerProvider;

  public WakeWordService_MembersInjector(
      Provider<NotificationManager> notificationManagerProvider) {
    this.notificationManagerProvider = notificationManagerProvider;
  }

  public static MembersInjector<WakeWordService> create(
      Provider<NotificationManager> notificationManagerProvider) {
    return new WakeWordService_MembersInjector(notificationManagerProvider);
  }

  @Override
  public void injectMembers(WakeWordService instance) {
    injectNotificationManager(instance, notificationManagerProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.services.WakeWordService.notificationManager")
  public static void injectNotificationManager(WakeWordService instance,
      NotificationManager notificationManager) {
    instance.notificationManager = notificationManager;
  }
}
