package com.zara.assistant.presentation.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

/**
 * Enhanced shape system for Zara with modern neumorphism design
 */
val ZaraShapes = Shapes(
    extraSmall = RoundedCornerShape(4.dp),
    small = RoundedCornerShape(8.dp),
    medium = RoundedCornerShape(16.dp),
    large = RoundedCornerShape(24.dp),
    extraLarge = RoundedCornerShape(32.dp)
)

// Custom shapes for specific components
object ZaraCustomShapes {
    val VoiceButton = RoundedCornerShape(50) // Circular
    val NeumorphismCard = RoundedCornerShape(20.dp)
    val NeumorphismButton = RoundedCornerShape(16.dp)
    val FloatingCard = RoundedCornerShape(28.dp)
    val BottomSheet = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
    val Dialog = RoundedCornerShape(24.dp)
    val Chip = RoundedCornerShape(50) // Fully rounded
    val ProgressBar = RoundedCornerShape(50) // Fully rounded
}
