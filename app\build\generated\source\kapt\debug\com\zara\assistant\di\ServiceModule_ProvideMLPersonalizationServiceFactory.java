// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.data.local.dao.UserLearningDao;
import com.zara.assistant.services.MLPersonalizationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideMLPersonalizationServiceFactory implements Factory<MLPersonalizationService> {
  private final Provider<Context> contextProvider;

  private final Provider<UserLearningDao> userLearningDaoProvider;

  public ServiceModule_ProvideMLPersonalizationServiceFactory(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider) {
    this.contextProvider = contextProvider;
    this.userLearningDaoProvider = userLearningDaoProvider;
  }

  @Override
  public MLPersonalizationService get() {
    return provideMLPersonalizationService(contextProvider.get(), userLearningDaoProvider.get());
  }

  public static ServiceModule_ProvideMLPersonalizationServiceFactory create(
      Provider<Context> contextProvider, Provider<UserLearningDao> userLearningDaoProvider) {
    return new ServiceModule_ProvideMLPersonalizationServiceFactory(contextProvider, userLearningDaoProvider);
  }

  public static MLPersonalizationService provideMLPersonalizationService(Context context,
      UserLearningDao userLearningDao) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideMLPersonalizationService(context, userLearningDao));
  }
}
