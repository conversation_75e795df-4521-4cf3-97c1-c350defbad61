package com.zara.assistant.domain.model;

/**
 * Represents the current state of the voice assistant
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u00002\u00020\u0001:\u00011B[\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\t\u0010#\u001a\u00020\u000fH\u00c6\u0003Ji\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001J\t\u0010%\u001a\u00020&H\u00d6\u0001J\u0013\u0010\'\u001a\u00020\u00052\b\u0010(\u001a\u0004\u0018\u00010)H\u00d6\u0003J\t\u0010*\u001a\u00020&H\u00d6\u0001J\t\u0010+\u001a\u00020\fH\u00d6\u0001J\u0019\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020&H\u00d6\u0001R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\r\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0018R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0018R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0018R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u00062"}, d2 = {"Lcom/zara/assistant/domain/model/VoiceState;", "Landroid/os/Parcelable;", "currentState", "Lcom/zara/assistant/domain/model/VoiceState$State;", "isWakeWordActive", "", "isListening", "isProcessing", "isSpeaking", "lastActivity", "Ljava/util/Date;", "currentCommand", "", "errorMessage", "confidence", "", "(Lcom/zara/assistant/domain/model/VoiceState$State;ZZZZLjava/util/Date;Ljava/lang/String;Ljava/lang/String;F)V", "getConfidence", "()F", "getCurrentCommand", "()Ljava/lang/String;", "getCurrentState", "()Lcom/zara/assistant/domain/model/VoiceState$State;", "getErrorMessage", "()Z", "getLastActivity", "()Ljava/util/Date;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "State", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class VoiceState implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.VoiceState.State currentState = null;
    private final boolean isWakeWordActive = false;
    private final boolean isListening = false;
    private final boolean isProcessing = false;
    private final boolean isSpeaking = false;
    @org.jetbrains.annotations.Nullable()
    private final java.util.Date lastActivity = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String currentCommand = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    private final float confidence = 0.0F;
    
    public VoiceState(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState.State currentState, boolean isWakeWordActive, boolean isListening, boolean isProcessing, boolean isSpeaking, @org.jetbrains.annotations.Nullable()
    java.util.Date lastActivity, @org.jetbrains.annotations.Nullable()
    java.lang.String currentCommand, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, float confidence) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.VoiceState.State getCurrentState() {
        return null;
    }
    
    public final boolean isWakeWordActive() {
        return false;
    }
    
    public final boolean isListening() {
        return false;
    }
    
    public final boolean isProcessing() {
        return false;
    }
    
    public final boolean isSpeaking() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.Date getLastActivity() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentCommand() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.VoiceState.State component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.Date component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.VoiceState copy(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState.State currentState, boolean isWakeWordActive, boolean isListening, boolean isProcessing, boolean isSpeaking, @org.jetbrains.annotations.Nullable()
    java.util.Date lastActivity, @org.jetbrains.annotations.Nullable()
    java.lang.String currentCommand, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, float confidence) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    /**
     * Voice assistant states
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/zara/assistant/domain/model/VoiceState$State;", "", "(Ljava/lang/String;I)V", "IDLE", "LISTENING_WAKE_WORD", "LISTENING_COMMAND", "PROCESSING_COMMAND", "GENERATING_RESPONSE", "SPEAKING_RESPONSE", "EXECUTING_ACTION", "ERROR", "DISABLED", "app_debug"})
    public static enum State {
        /*public static final*/ IDLE /* = new IDLE() */,
        /*public static final*/ LISTENING_WAKE_WORD /* = new LISTENING_WAKE_WORD() */,
        /*public static final*/ LISTENING_COMMAND /* = new LISTENING_COMMAND() */,
        /*public static final*/ PROCESSING_COMMAND /* = new PROCESSING_COMMAND() */,
        /*public static final*/ GENERATING_RESPONSE /* = new GENERATING_RESPONSE() */,
        /*public static final*/ SPEAKING_RESPONSE /* = new SPEAKING_RESPONSE() */,
        /*public static final*/ EXECUTING_ACTION /* = new EXECUTING_ACTION() */,
        /*public static final*/ ERROR /* = new ERROR() */,
        /*public static final*/ DISABLED /* = new DISABLED() */;
        
        State() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.VoiceState.State> getEntries() {
            return null;
        }
    }
}