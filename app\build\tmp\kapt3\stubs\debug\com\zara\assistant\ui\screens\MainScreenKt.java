package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a6\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a$\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\rH\u0003\u001a(\u0010\u000f\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a$\u0010\u0011\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a2\u0010\u0014\u001a\u00020\u00012\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u00162\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a*\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u00172\u0006\u0010\u001e\u001a\u00020\u000e2\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a\u0010\u0010\u001f\u001a\u00020\u00172\u0006\u0010 \u001a\u00020\u0017H\u0002\u001a\u0010\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$H\u0002\u00a8\u0006%"}, d2 = {"MainScreen", "", "viewModel", "Lcom/zara/assistant/ui/viewmodel/MainViewModel;", "onNavigateToSettings", "Lkotlin/Function0;", "onNavigateToCommands", "modifier", "Landroidx/compose/ui/Modifier;", "ModernBottomSection", "uiState", "Lcom/zara/assistant/ui/viewmodel/MainUiState;", "onWakeWordToggle", "Lkotlin/Function1;", "", "ModernCenterSection", "onVoiceButtonClick", "ModernTopSection", "onSettingsClick", "onCommandsClick", "PermissionDialog", "missingPermissions", "", "", "onRequestPermissions", "onDismiss", "StatusIndicator", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "label", "isActive", "getPermissionDisplayName", "permission", "mapToConstantsVoiceState", "Lcom/zara/assistant/core/Constants$VoiceState;", "state", "Lcom/zara/assistant/domain/model/VoiceState$State;", "app_debug"})
public final class MainScreenKt {
    
    /**
     * Main screen of the Zara AI Voice Assistant
     */
    @androidx.compose.runtime.Composable()
    public static final void MainScreen(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.ui.viewmodel.MainViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCommands, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ModernTopSection(kotlin.jvm.functions.Function0<kotlin.Unit> onSettingsClick, kotlin.jvm.functions.Function0<kotlin.Unit> onCommandsClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ModernCenterSection(com.zara.assistant.ui.viewmodel.MainUiState uiState, kotlin.jvm.functions.Function0<kotlin.Unit> onVoiceButtonClick, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ModernBottomSection(com.zara.assistant.ui.viewmodel.MainUiState uiState, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onWakeWordToggle) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatusIndicator(androidx.compose.ui.graphics.vector.ImageVector icon, java.lang.String label, boolean isActive, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PermissionDialog(java.util.List<java.lang.String> missingPermissions, kotlin.jvm.functions.Function0<kotlin.Unit> onRequestPermissions, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    private static final java.lang.String getPermissionDisplayName(java.lang.String permission) {
        return null;
    }
    
    private static final com.zara.assistant.core.Constants.VoiceState mapToConstantsVoiceState(com.zara.assistant.domain.model.VoiceState.State state) {
        return null;
    }
}