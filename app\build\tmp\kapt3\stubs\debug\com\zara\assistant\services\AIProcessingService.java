package com.zara.assistant.services;

/**
 * Service for handling AI processing and response generation
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0088\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\b\u0007\u0018\u0000 >2\u00020\u0001:\u0002=>B\u0005\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u001fH\u0086@\u00a2\u0006\u0002\u0010 J$\u0010!\u001a\b\u0012\u0004\u0012\u00020#0\"2\u0006\u0010$\u001a\u00020%H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b&\u0010\'J8\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00050\"2\u0006\u0010)\u001a\u00020*2\b\b\u0002\u0010+\u001a\u00020,2\b\b\u0002\u0010-\u001a\u00020.H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b/\u00100J\u0006\u00101\u001a\u00020\u0007J\u0012\u00102\u001a\u0002032\b\u00104\u001a\u0004\u0018\u000105H\u0016J\b\u00106\u001a\u000207H\u0016J\"\u00108\u001a\u0002092\b\u00104\u001a\u0004\u0018\u0001052\u0006\u0010:\u001a\u0002092\u0006\u0010;\u001a\u000209H\u0016J\u0010\u0010<\u001a\u0002072\u0006\u0010)\u001a\u00020*H\u0002R\u0016\u0010\u0003\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\b\u001a\u00020\t8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u0012\u0010\u000e\u001a\u00060\u000fR\u00020\u0000X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u0010\u001a\u00020\u00118\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015R\u0019\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006?"}, d2 = {"Lcom/zara/assistant/services/AIProcessingService;", "Landroid/app/Service;", "()V", "_lastResponse", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/domain/model/AIResponse;", "_processingState", "", "aiRepository", "Lcom/zara/assistant/domain/repository/AIRepository;", "getAiRepository", "()Lcom/zara/assistant/domain/repository/AIRepository;", "setAiRepository", "(Lcom/zara/assistant/domain/repository/AIRepository;)V", "binder", "Lcom/zara/assistant/services/AIProcessingService$AIProcessingBinder;", "conversationRepository", "Lcom/zara/assistant/domain/repository/ConversationRepository;", "getConversationRepository", "()Lcom/zara/assistant/domain/repository/ConversationRepository;", "setConversationRepository", "(Lcom/zara/assistant/domain/repository/ConversationRepository;)V", "lastResponse", "Lkotlinx/coroutines/flow/StateFlow;", "getLastResponse", "()Lkotlinx/coroutines/flow/StateFlow;", "processingState", "getProcessingState", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "checkAIHealth", "Lkotlin/Pair;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "classifyCommand", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/CommandType;", "text", "", "classifyCommand-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateResponse", "command", "Lcom/zara/assistant/domain/model/VoiceCommand;", "personality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "responseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "generateResponse-BWLJW6A", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lcom/zara/assistant/core/Constants$AIPersonality;Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isProcessing", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onDestroy", "", "onStartCommand", "", "flags", "startId", "processVoiceCommand", "AIProcessingBinder", "Companion", "app_debug"})
public final class AIProcessingService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AIProcessingService";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_PROCESS_COMMAND = "PROCESS_COMMAND";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_VOICE_COMMAND = "voice_command";
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.AIProcessingService.AIProcessingBinder binder = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _processingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> processingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.AIResponse> _lastResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.AIResponse> lastResponse = null;
    @javax.inject.Inject()
    public com.zara.assistant.domain.repository.AIRepository aiRepository;
    @javax.inject.Inject()
    public com.zara.assistant.domain.repository.ConversationRepository conversationRepository;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.AIProcessingService.Companion Companion = null;
    
    public AIProcessingService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getProcessingState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.domain.model.AIResponse> getLastResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.repository.AIRepository getAiRepository() {
        return null;
    }
    
    public final void setAiRepository(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.AIRepository p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.repository.ConversationRepository getConversationRepository() {
        return null;
    }
    
    public final void setConversationRepository(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.ConversationRepository p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    private final void processVoiceCommand(com.zara.assistant.domain.model.VoiceCommand command) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object checkAIHealth(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Pair<java.lang.Boolean, java.lang.Boolean>> $completion) {
        return null;
    }
    
    public final boolean isProcessing() {
        return false;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/services/AIProcessingService$AIProcessingBinder;", "Landroid/os/Binder;", "(Lcom/zara/assistant/services/AIProcessingService;)V", "getService", "Lcom/zara/assistant/services/AIProcessingService;", "app_debug"})
    public final class AIProcessingBinder extends android.os.Binder {
        
        public AIProcessingBinder() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.zara.assistant.services.AIProcessingService getService() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/zara/assistant/services/AIProcessingService$Companion;", "", "()V", "ACTION_PROCESS_COMMAND", "", "EXTRA_VOICE_COMMAND", "TAG", "processCommand", "", "context", "Landroid/content/Context;", "command", "Lcom/zara/assistant/domain/model/VoiceCommand;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void processCommand(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        com.zara.assistant.domain.model.VoiceCommand command) {
        }
    }
}