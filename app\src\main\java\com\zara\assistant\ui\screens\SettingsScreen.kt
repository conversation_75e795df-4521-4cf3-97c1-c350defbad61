package com.zara.assistant.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.zara.assistant.R
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.ui.viewmodel.SettingsViewModel
import com.zara.assistant.presentation.utils.AccessibilityUtils
import com.zara.assistant.presentation.utils.PerformanceUtils

// Data classes for modern settings structure
data class SettingsCategory(
    val title: String,
    val icon: ImageVector,
    val items: List<SettingItem>
)

sealed class SettingItem {
    data class Toggle(
        val title: String,
        val subtitle: String,
        val isChecked: Boolean,
        val onToggle: (Boolean) -> Unit
    ) : SettingItem()

    data class Slider(
        val title: String,
        val subtitle: String,
        val value: Float,
        val range: ClosedFloatingPointRange<Float>,
        val onValueChange: (Float) -> Unit
    ) : SettingItem()

    data class Action(
        val title: String,
        val subtitle: String,
        val onClick: () -> Unit
    ) : SettingItem()
}

/**
 * Modern settings screen for configuring Zara with enhanced UI
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel,
    onNavigateBack: () -> Unit,
    onNavigateToAbout: () -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.settings),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.background,
                            MaterialTheme.colorScheme.surface
                        )
                    )
                )
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(horizontal = 24.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                item {
                    Spacer(modifier = Modifier.height(8.dp))
                }

                // Settings Categories
                val settingsCategories = listOf(
                    SettingsCategory(
                        title = "Voice & Audio",
                        icon = Icons.Default.Mic,
                        items = listOf(
                            SettingItem.Toggle(
                                title = "Wake Word Detection",
                                subtitle = "Say \"Hey Zara\" to activate",
                                isChecked = uiState.isWakeWordEnabled,
                                onToggle = viewModel::onWakeWordToggle
                            ),
                            SettingItem.Slider(
                                title = "Speech Rate",
                                subtitle = "How fast Zara speaks",
                                value = uiState.speechRate,
                                range = 0.5f..2.0f,
                                onValueChange = viewModel::onSpeechRateChange
                            ),
                            SettingItem.Slider(
                                title = "Speech Pitch",
                                subtitle = "Voice pitch adjustment",
                                value = uiState.speechPitch,
                                range = 0.5f..2.0f,
                                onValueChange = viewModel::onSpeechPitchChange
                            )
                        )
                    ),
                    SettingsCategory(
                        title = "Appearance",
                        icon = Icons.Default.Palette,
                        items = listOf(
                            SettingItem.Toggle(
                                title = "Dark Theme",
                                subtitle = "Use dark color scheme",
                                isChecked = uiState.isDarkTheme,
                                onToggle = viewModel::onThemeToggle
                            )
                        )
                    ),
                    SettingsCategory(
                        title = "Privacy & Security",
                        icon = Icons.Default.Security,
                        items = listOf(
                            SettingItem.Toggle(
                                title = "Auto Listen",
                                subtitle = "Continue listening after response",
                                isChecked = uiState.autoListen,
                                onToggle = viewModel::onAutoListenToggle
                            )
                        )
                    )
                )

                items(settingsCategories) { category ->
                    ModernSettingsSection(
                        category = category,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                // About Section
                item {
                    ModernSettingsSection(
                        category = SettingsCategory(
                            title = "About",
                            icon = Icons.Default.Info,
                            items = listOf(
                                SettingItem.Action(
                                    title = "About Zara",
                                    subtitle = "Version info and credits",
                                    onClick = onNavigateToAbout
                                )
                            )
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(24.dp))
                }
            }
        }
    }
}

@Composable
private fun ModernSettingsSection(
    category: SettingsCategory,
    modifier: Modifier = Modifier
) {
    NeumorphismCard(
        modifier = modifier,
        elevation = 12.dp,
        contentPadding = 0.dp
    ) {
        Column {
            // Section Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = category.icon,
                        contentDescription = category.title,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                }

                Text(
                    text = category.title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            // Section Items
            category.items.forEachIndexed { index, item ->
                when (item) {
                    is SettingItem.Toggle -> {
                        ModernToggleItem(
                            title = item.title,
                            subtitle = item.subtitle,
                            isChecked = item.isChecked,
                            onToggle = item.onToggle,
                            modifier = Modifier.padding(horizontal = 20.dp, vertical = 8.dp)
                        )
                    }
                    is SettingItem.Slider -> {
                        ModernSliderItem(
                            title = item.title,
                            subtitle = item.subtitle,
                            value = item.value,
                            range = item.range,
                            onValueChange = item.onValueChange,
                            modifier = Modifier.padding(horizontal = 20.dp, vertical = 8.dp)
                        )
                    }
                    is SettingItem.Action -> {
                        ModernActionItem(
                            title = item.title,
                            subtitle = item.subtitle,
                            onClick = item.onClick,
                            modifier = Modifier.padding(horizontal = 20.dp, vertical = 8.dp)
                        )
                    }
                }

                if (index < category.items.size - 1) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }

            Spacer(modifier = Modifier.height(12.dp))
        }
    }
}

@Composable
private fun ModernToggleItem(
    title: String,
    subtitle: String,
    isChecked: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }

        Switch(
            checked = isChecked,
            onCheckedChange = onToggle,
            modifier = Modifier.semantics {
                contentDescription = AccessibilityUtils.getSettingDescription(title, isChecked)
            }
        )
    }
}

@Composable
private fun ModernSliderItem(
    title: String,
    subtitle: String,
    value: Float,
    range: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }

            Text(
                text = String.format("%.1f", value),
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = range,
            modifier = Modifier
                .fillMaxWidth()
                .semantics {
                    contentDescription = AccessibilityUtils.getSliderDescription(title, value, range)
                }
        )
    }
}

@Composable
private fun ModernActionItem(
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    NeumorphismCard(
        modifier = modifier.fillMaxWidth(),
        elevation = 6.dp,
        contentPadding = 16.dp,
        onClick = onClick
    ) {
        Column {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}
