// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import com.zara.assistant.data.remote.interceptor.AuthInterceptor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvidePerplexityAuthInterceptorFactory implements Factory<AuthInterceptor> {
  @Override
  public AuthInterceptor get() {
    return providePerplexityAuthInterceptor();
  }

  public static NetworkModule_ProvidePerplexityAuthInterceptorFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AuthInterceptor providePerplexityAuthInterceptor() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.providePerplexityAuthInterceptor());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvidePerplexityAuthInterceptorFactory INSTANCE = new NetworkModule_ProvidePerplexityAuthInterceptorFactory();
  }
}
