package com.zara.assistant.domain.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0003\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0004\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0005\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0006\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0007\u001a\u00020\u0001*\u00020\u0002\u00a8\u0006\b"}, d2 = {"isAppControl", "", "Lcom/zara/assistant/domain/model/ActionType;", "isDestructive", "isMediaControl", "isNavigation", "isSystemControl", "requiresPermission", "app_debug"})
public final class SystemActionKt {
    
    /**
     * Extension functions for ActionType
     */
    public static final boolean isSystemControl(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ActionType $this$isSystemControl) {
        return false;
    }
    
    public static final boolean isAppControl(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ActionType $this$isAppControl) {
        return false;
    }
    
    public static final boolean isNavigation(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ActionType $this$isNavigation) {
        return false;
    }
    
    public static final boolean isMediaControl(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ActionType $this$isMediaControl) {
        return false;
    }
    
    public static final boolean requiresPermission(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ActionType $this$requiresPermission) {
        return false;
    }
    
    public static final boolean isDestructive(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ActionType $this$isDestructive) {
        return false;
    }
}