package com.zara.assistant.domain.model;

/**
 * Conversation context
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B[\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u00032\u0006\u0010%\u001a\u00020\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0005H\u00c6\u0003J\t\u0010(\u001a\u00020\u0007H\u00c6\u0003J\t\u0010)\u001a\u00020\tH\u00c6\u0003J\t\u0010*\u001a\u00020\u000bH\u00c6\u0003J\u000f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u000e0\rH\u00c6\u0003J\t\u0010,\u001a\u00020\u0010H\u00c6\u0003J\t\u0010-\u001a\u00020\u0010H\u00c6\u0003J_\u0010.\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0010H\u00c6\u0001J\u0013\u0010/\u001a\u0002002\b\u00101\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00102\u001a\u000203H\u00d6\u0001J\u0006\u00104\u001a\u000200J\t\u00105\u001a\u00020\u0003H\u00d6\u0001J\u0006\u00106\u001a\u00020\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0018R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!\u00a8\u00067"}, d2 = {"Lcom/zara/assistant/domain/model/ConversationContext;", "", "conversationId", "", "type", "Lcom/zara/assistant/domain/model/ConversationType;", "sessionType", "Lcom/zara/assistant/domain/model/ConversationSessionType;", "state", "Lcom/zara/assistant/domain/model/ConversationState;", "parameters", "Lcom/zara/assistant/domain/model/CommandParameters;", "conversationHistory", "", "Lcom/zara/assistant/domain/model/ConversationTurn;", "startTime", "Ljava/util/Date;", "lastActivity", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/ConversationType;Lcom/zara/assistant/domain/model/ConversationSessionType;Lcom/zara/assistant/domain/model/ConversationState;Lcom/zara/assistant/domain/model/CommandParameters;Ljava/util/List;Ljava/util/Date;Ljava/util/Date;)V", "getConversationHistory", "()Ljava/util/List;", "getConversationId", "()Ljava/lang/String;", "getLastActivity", "()Ljava/util/Date;", "getParameters", "()Lcom/zara/assistant/domain/model/CommandParameters;", "getSessionType", "()Lcom/zara/assistant/domain/model/ConversationSessionType;", "getStartTime", "getState", "()Lcom/zara/assistant/domain/model/ConversationState;", "getType", "()Lcom/zara/assistant/domain/model/ConversationType;", "addTurn", "", "userInput", "zaraResponse", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "", "shouldContinueAfterExecution", "toString", "updateActivity", "app_debug"})
public final class ConversationContext {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String conversationId = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.ConversationType type = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.ConversationSessionType sessionType = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.ConversationState state = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.CommandParameters parameters = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.zara.assistant.domain.model.ConversationTurn> conversationHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Date startTime = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Date lastActivity = null;
    
    public ConversationContext(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationType type, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationSessionType sessionType, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationState state, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.CommandParameters parameters, @org.jetbrains.annotations.NotNull()
    java.util.List<com.zara.assistant.domain.model.ConversationTurn> conversationHistory, @org.jetbrains.annotations.NotNull()
    java.util.Date startTime, @org.jetbrains.annotations.NotNull()
    java.util.Date lastActivity) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConversationId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationType getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationSessionType getSessionType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationState getState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getParameters() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.zara.assistant.domain.model.ConversationTurn> getConversationHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getStartTime() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getLastActivity() {
        return null;
    }
    
    public final void addTurn(@org.jetbrains.annotations.NotNull()
    java.lang.String userInput, @org.jetbrains.annotations.NotNull()
    java.lang.String zaraResponse) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationContext updateActivity() {
        return null;
    }
    
    public final boolean shouldContinueAfterExecution() {
        return false;
    }
    
    public ConversationContext() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationType component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationSessionType component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationState component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.zara.assistant.domain.model.ConversationTurn> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.ConversationContext copy(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationType type, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationSessionType sessionType, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationState state, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.CommandParameters parameters, @org.jetbrains.annotations.NotNull()
    java.util.List<com.zara.assistant.domain.model.ConversationTurn> conversationHistory, @org.jetbrains.annotations.NotNull()
    java.util.Date startTime, @org.jetbrains.annotations.NotNull()
    java.util.Date lastActivity) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}