package com.zara.assistant.domain.repository;

/**
 * Repository interface for voice-related operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001f\bf\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH&J\u000e\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH&J\u000e\u0010\f\u001a\u00020\rH\u00a6@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\bH&J\u000e\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\bH&J\u000e\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00030\bH&J$\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0006\u0010\u0015\u001a\u00020\u0016H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018J\u001c\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00030\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u0004J\u0016\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\u00a6@\u00a2\u0006\u0002\u0010\u001fJ$\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001c0\u00132\u0006\u0010!\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J\u001c\u0010$\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010\u0004J\u001c\u0010&\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\'\u0010\u0004J\u001c\u0010(\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b)\u0010\u0004J\u001c\u0010*\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b+\u0010\u0004J\u001c\u0010,\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b-\u0010\u0004J\u001c\u0010.\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b/\u0010\u0004J\u001c\u00100\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b1\u0010\u0004J$\u00102\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00132\u0006\u00103\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b4\u00105J$\u00106\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00132\u0006\u00107\u001a\u00020\rH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b8\u00109J\u0016\u0010:\u001a\u00020\u001c2\u0006\u0010;\u001a\u00020\u000fH\u00a6@\u00a2\u0006\u0002\u0010<\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006="}, d2 = {"Lcom/zara/assistant/domain/repository/VoiceRepository;", "", "checkMicrophonePermission", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAudioConfig", "Lcom/zara/assistant/domain/model/AudioConfig;", "getAudioLevel", "Lkotlinx/coroutines/flow/Flow;", "", "getRecognizedText", "", "getVoiceSettings", "Lcom/zara/assistant/domain/model/VoiceSettings;", "getVoiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "isSpeaking", "isWakeWordDetectionActive", "processVoiceCommand", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/VoiceCommand;", "audioData", "", "processVoiceCommand-gIAlu-s", "([BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "requestMicrophonePermission", "requestMicrophonePermission-IoAF18A", "setVoiceState", "", "newState", "Lcom/zara/assistant/domain/model/VoiceState$State;", "(Lcom/zara/assistant/domain/model/VoiceState$State;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "speak", "text", "speak-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startAudioSession", "startAudioSession-IoAF18A", "startListening", "startListening-IoAF18A", "startWakeWordDetection", "startWakeWordDetection-IoAF18A", "stopAudioSession", "stopAudioSession-IoAF18A", "stopListening", "stopListening-IoAF18A", "stopSpeaking", "stopSpeaking-IoAF18A", "stopWakeWordDetection", "stopWakeWordDetection-IoAF18A", "updateAudioConfig", "config", "updateAudioConfig-gIAlu-s", "(Lcom/zara/assistant/domain/model/AudioConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVoiceSettings", "settings", "updateVoiceSettings-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVoiceState", "state", "(Lcom/zara/assistant/domain/model/VoiceState;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface VoiceRepository {
    
    /**
     * Voice state management
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.VoiceState> getVoiceState();
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateVoiceState(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setVoiceState(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState.State newState, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> isWakeWordDetectionActive();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.String> getRecognizedText();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> isSpeaking();
    
    /**
     * Voice settings
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getVoiceSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.VoiceSettings> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAudioConfig(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AudioConfig> $completion);
    
    /**
     * Permissions
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object checkMicrophonePermission(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Float> getAudioLevel();
}