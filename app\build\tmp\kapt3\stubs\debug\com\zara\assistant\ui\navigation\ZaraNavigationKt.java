package com.zara.assistant.ui.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\n"}, d2 = {"ZaraNavigation", "", "mainViewModel", "Lcom/zara/assistant/ui/viewmodel/MainViewModel;", "modifier", "Landroidx/compose/ui/Modifier;", "navController", "Landroidx/navigation/NavHostController;", "startDestination", "", "app_debug"})
public final class ZaraNavigationKt {
    
    /**
     * Main navigation component for Zara app
     */
    @androidx.compose.runtime.Composable()
    public static final void ZaraNavigation(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.ui.viewmodel.MainViewModel mainViewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String startDestination) {
    }
}