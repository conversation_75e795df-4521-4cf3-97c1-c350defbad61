package com.zara.assistant.ui.viewmodel;

/**
 * ViewModel for the settings screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\f\u001a\u00020\rJ\b\u0010\u000e\u001a\u00020\rH\u0002J\u000e\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0013\u001a\u00020\rJ\u000e\u0010\u0014\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0015\u001a\u00020\rJ\u000e\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\u0018J\u000e\u0010\u0019\u001a\u00020\r2\u0006\u0010\u001a\u001a\u00020\u001bJ\u000e\u0010\u001c\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\u001eJ\u000e\u0010\u001f\u001a\u00020\r2\u0006\u0010 \u001a\u00020\u001eJ\u000e\u0010!\u001a\u00020\r2\u0006\u0010\"\u001a\u00020\u001eJ\u000e\u0010#\u001a\u00020\r2\u0006\u0010$\u001a\u00020%J\u000e\u0010&\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\'\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006("}, d2 = {"Lcom/zara/assistant/ui/viewmodel/SettingsViewModel;", "Landroidx/lifecycle/ViewModel;", "settingsRepository", "Lcom/zara/assistant/domain/repository/SettingsRepository;", "(Lcom/zara/assistant/domain/repository/SettingsRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/ui/viewmodel/SettingsUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "dismissMessage", "", "observeSettings", "onAnalyticsToggle", "enabled", "", "onAutoListenToggle", "onClearData", "onConversationHistoryToggle", "onExportData", "onPersonalityChange", "personality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "onResponseStyleChange", "style", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "onSensitivityChange", "sensitivity", "", "onSpeechPitchChange", "pitch", "onSpeechRateChange", "rate", "onThemeModeChange", "mode", "Lcom/zara/assistant/domain/repository/ThemeMode;", "onVoiceDataStorageToggle", "onWakeWordToggle", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SettingsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.SettingsRepository settingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.ui.viewmodel.SettingsUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.ui.viewmodel.SettingsUiState> uiState = null;
    
    @javax.inject.Inject()
    public SettingsViewModel(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.SettingsRepository settingsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.ui.viewmodel.SettingsUiState> getUiState() {
        return null;
    }
    
    private final void observeSettings() {
    }
    
    public final void onWakeWordToggle(boolean enabled) {
    }
    
    public final void onSensitivityChange(float sensitivity) {
    }
    
    public final void onSpeechRateChange(float rate) {
    }
    
    public final void onSpeechPitchChange(float pitch) {
    }
    
    public final void onAutoListenToggle(boolean enabled) {
    }
    
    public final void onPersonalityChange(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIPersonality personality) {
    }
    
    public final void onResponseStyleChange(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIResponseStyle style) {
    }
    
    public final void onConversationHistoryToggle(boolean enabled) {
    }
    
    public final void onVoiceDataStorageToggle(boolean enabled) {
    }
    
    public final void onAnalyticsToggle(boolean enabled) {
    }
    
    public final void onThemeModeChange(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.ThemeMode mode) {
    }
    
    public final void onClearData() {
    }
    
    public final void onExportData() {
    }
    
    public final void dismissMessage() {
    }
}