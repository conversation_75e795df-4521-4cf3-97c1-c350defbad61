package com.zara.assistant.domain.model;

/**
 * Types of system actions that can be performed
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b;\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019j\u0002\b\u001aj\u0002\b\u001bj\u0002\b\u001cj\u0002\b\u001dj\u0002\b\u001ej\u0002\b\u001fj\u0002\b j\u0002\b!j\u0002\b\"j\u0002\b#j\u0002\b$j\u0002\b%j\u0002\b&j\u0002\b\'j\u0002\b(j\u0002\b)j\u0002\b*j\u0002\b+j\u0002\b,j\u0002\b-j\u0002\b.j\u0002\b/j\u0002\b0j\u0002\b1j\u0002\b2j\u0002\b3j\u0002\b4j\u0002\b5j\u0002\b6j\u0002\b7j\u0002\b8j\u0002\b9j\u0002\b:j\u0002\b;\u00a8\u0006<"}, d2 = {"Lcom/zara/assistant/domain/model/ActionType;", "", "(Ljava/lang/String;I)V", "OPEN_APP", "CLOSE_APP", "SWITCH_APP", "KILL_APP", "TOGGLE_WIFI", "TOGGLE_BLUETOOTH", "TOGGLE_AIRPLANE_MODE", "TOGGLE_MOBILE_DATA", "TOGGLE_HOTSPOT", "TOGGLE_NFC", "TOGGLE_LOCATION", "TOGGLE_AUTO_ROTATE", "TOGGLE_DO_NOT_DISTURB", "ADJUST_VOLUME", "SET_VOLUME", "MUTE_VOLUME", "SET_BRIGHTNESS", "ADJUST_BRIGHTNESS", "TOGGLE_FLASHLIGHT", "GO_HOME", "GO_BACK", "OPEN_RECENT_APPS", "OPEN_NOTIFICATIONS", "OPEN_QUICK_SETTINGS", "TAKE_SCREENSHOT", "LOCK_SCREEN", "POWER_OFF", "RESTART", "MAKE_CALL", "SEND_SMS", "SEND_EMAIL", "PLAY_MUSIC", "PAUSE_MUSIC", "NEXT_TRACK", "PREVIOUS_TRACK", "STOP_MUSIC", "OPEN_MAPS", "GET_DIRECTIONS", "SHARE_LOCATION", "SET_ALARM", "SET_TIMER", "SET_REMINDER", "CREATE_EVENT", "OPEN_SETTINGS", "OPEN_WIFI_SETTINGS", "OPEN_BLUETOOTH_SETTINGS", "OPEN_DISPLAY_SETTINGS", "OPEN_SOUND_SETTINGS", "OPEN_SECURITY_SETTINGS", "OPEN_ACCESSIBILITY_SETTINGS", "OPEN_FILE", "SHARE_FILE", "DELETE_FILE", "OPEN_BROWSER", "SEARCH_WEB", "OPEN_URL", "UNKNOWN", "app_debug"})
public enum ActionType {
    /*public static final*/ OPEN_APP /* = new OPEN_APP() */,
    /*public static final*/ CLOSE_APP /* = new CLOSE_APP() */,
    /*public static final*/ SWITCH_APP /* = new SWITCH_APP() */,
    /*public static final*/ KILL_APP /* = new KILL_APP() */,
    /*public static final*/ TOGGLE_WIFI /* = new TOGGLE_WIFI() */,
    /*public static final*/ TOGGLE_BLUETOOTH /* = new TOGGLE_BLUETOOTH() */,
    /*public static final*/ TOGGLE_AIRPLANE_MODE /* = new TOGGLE_AIRPLANE_MODE() */,
    /*public static final*/ TOGGLE_MOBILE_DATA /* = new TOGGLE_MOBILE_DATA() */,
    /*public static final*/ TOGGLE_HOTSPOT /* = new TOGGLE_HOTSPOT() */,
    /*public static final*/ TOGGLE_NFC /* = new TOGGLE_NFC() */,
    /*public static final*/ TOGGLE_LOCATION /* = new TOGGLE_LOCATION() */,
    /*public static final*/ TOGGLE_AUTO_ROTATE /* = new TOGGLE_AUTO_ROTATE() */,
    /*public static final*/ TOGGLE_DO_NOT_DISTURB /* = new TOGGLE_DO_NOT_DISTURB() */,
    /*public static final*/ ADJUST_VOLUME /* = new ADJUST_VOLUME() */,
    /*public static final*/ SET_VOLUME /* = new SET_VOLUME() */,
    /*public static final*/ MUTE_VOLUME /* = new MUTE_VOLUME() */,
    /*public static final*/ SET_BRIGHTNESS /* = new SET_BRIGHTNESS() */,
    /*public static final*/ ADJUST_BRIGHTNESS /* = new ADJUST_BRIGHTNESS() */,
    /*public static final*/ TOGGLE_FLASHLIGHT /* = new TOGGLE_FLASHLIGHT() */,
    /*public static final*/ GO_HOME /* = new GO_HOME() */,
    /*public static final*/ GO_BACK /* = new GO_BACK() */,
    /*public static final*/ OPEN_RECENT_APPS /* = new OPEN_RECENT_APPS() */,
    /*public static final*/ OPEN_NOTIFICATIONS /* = new OPEN_NOTIFICATIONS() */,
    /*public static final*/ OPEN_QUICK_SETTINGS /* = new OPEN_QUICK_SETTINGS() */,
    /*public static final*/ TAKE_SCREENSHOT /* = new TAKE_SCREENSHOT() */,
    /*public static final*/ LOCK_SCREEN /* = new LOCK_SCREEN() */,
    /*public static final*/ POWER_OFF /* = new POWER_OFF() */,
    /*public static final*/ RESTART /* = new RESTART() */,
    /*public static final*/ MAKE_CALL /* = new MAKE_CALL() */,
    /*public static final*/ SEND_SMS /* = new SEND_SMS() */,
    /*public static final*/ SEND_EMAIL /* = new SEND_EMAIL() */,
    /*public static final*/ PLAY_MUSIC /* = new PLAY_MUSIC() */,
    /*public static final*/ PAUSE_MUSIC /* = new PAUSE_MUSIC() */,
    /*public static final*/ NEXT_TRACK /* = new NEXT_TRACK() */,
    /*public static final*/ PREVIOUS_TRACK /* = new PREVIOUS_TRACK() */,
    /*public static final*/ STOP_MUSIC /* = new STOP_MUSIC() */,
    /*public static final*/ OPEN_MAPS /* = new OPEN_MAPS() */,
    /*public static final*/ GET_DIRECTIONS /* = new GET_DIRECTIONS() */,
    /*public static final*/ SHARE_LOCATION /* = new SHARE_LOCATION() */,
    /*public static final*/ SET_ALARM /* = new SET_ALARM() */,
    /*public static final*/ SET_TIMER /* = new SET_TIMER() */,
    /*public static final*/ SET_REMINDER /* = new SET_REMINDER() */,
    /*public static final*/ CREATE_EVENT /* = new CREATE_EVENT() */,
    /*public static final*/ OPEN_SETTINGS /* = new OPEN_SETTINGS() */,
    /*public static final*/ OPEN_WIFI_SETTINGS /* = new OPEN_WIFI_SETTINGS() */,
    /*public static final*/ OPEN_BLUETOOTH_SETTINGS /* = new OPEN_BLUETOOTH_SETTINGS() */,
    /*public static final*/ OPEN_DISPLAY_SETTINGS /* = new OPEN_DISPLAY_SETTINGS() */,
    /*public static final*/ OPEN_SOUND_SETTINGS /* = new OPEN_SOUND_SETTINGS() */,
    /*public static final*/ OPEN_SECURITY_SETTINGS /* = new OPEN_SECURITY_SETTINGS() */,
    /*public static final*/ OPEN_ACCESSIBILITY_SETTINGS /* = new OPEN_ACCESSIBILITY_SETTINGS() */,
    /*public static final*/ OPEN_FILE /* = new OPEN_FILE() */,
    /*public static final*/ SHARE_FILE /* = new SHARE_FILE() */,
    /*public static final*/ DELETE_FILE /* = new DELETE_FILE() */,
    /*public static final*/ OPEN_BROWSER /* = new OPEN_BROWSER() */,
    /*public static final*/ SEARCH_WEB /* = new SEARCH_WEB() */,
    /*public static final*/ OPEN_URL /* = new OPEN_URL() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    ActionType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.zara.assistant.domain.model.ActionType> getEntries() {
        return null;
    }
}