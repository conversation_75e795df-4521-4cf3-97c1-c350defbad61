// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.data.local.dao.UserLearningDao;
import com.zara.assistant.services.PersonalMemoryService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvidePersonalMemoryServiceFactory implements Factory<PersonalMemoryService> {
  private final Provider<Context> contextProvider;

  private final Provider<UserLearningDao> userLearningDaoProvider;

  public ServiceModule_ProvidePersonalMemoryServiceFactory(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider) {
    this.contextProvider = contextProvider;
    this.userLearningDaoProvider = userLearningDaoProvider;
  }

  @Override
  public PersonalMemoryService get() {
    return providePersonalMemoryService(contextProvider.get(), userLearningDaoProvider.get());
  }

  public static ServiceModule_ProvidePersonalMemoryServiceFactory create(
      Provider<Context> contextProvider, Provider<UserLearningDao> userLearningDaoProvider) {
    return new ServiceModule_ProvidePersonalMemoryServiceFactory(contextProvider, userLearningDaoProvider);
  }

  public static PersonalMemoryService providePersonalMemoryService(Context context,
      UserLearningDao userLearningDao) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.providePersonalMemoryService(context, userLearningDao));
  }
}
