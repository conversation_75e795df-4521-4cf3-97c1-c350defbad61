package com.zara.assistant.ui.viewmodel;

/**
 * ViewModel for the main screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0005\b\u0007\u0018\u0000 62\u00020\u0001:\u00016B!\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\u0016\u001a\u00020\u0017H\u0002J\u0006\u0010\u0018\u001a\u00020\u0017J\u0006\u0010\u0019\u001a\u00020\u0017J\u0006\u0010\u001a\u001a\u00020\u0017J\u0010\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\u0002J\b\u0010\u001f\u001a\u00020\u0017H\u0002J\b\u0010 \u001a\u00020\u0017H\u0002J\u0006\u0010!\u001a\u00020\u0017J\u0006\u0010\"\u001a\u00020\u0017J\u0006\u0010#\u001a\u00020\u0017J\u0006\u0010$\u001a\u00020\u0017J\u0006\u0010%\u001a\u00020\u0017J\u0006\u0010&\u001a\u00020\u0017J\u0006\u0010\'\u001a\u00020\u0017J\u0006\u0010(\u001a\u00020\u0017J\u0006\u0010)\u001a\u00020\u0017J\u0006\u0010*\u001a\u00020\u0017J\u000e\u0010+\u001a\u00020\u00172\u0006\u0010,\u001a\u00020-J\u000e\u0010.\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010/J\u0014\u00100\u001a\u00020\u00172\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u001c02J\u000e\u00103\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010/J\u000e\u00104\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010/J\u000e\u00105\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010/R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\f\u001a\u00020\r8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0010\u0010\u0011\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00067"}, d2 = {"Lcom/zara/assistant/ui/viewmodel/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "voiceRepository", "Lcom/zara/assistant/domain/repository/VoiceRepository;", "settingsRepository", "Lcom/zara/assistant/domain/repository/SettingsRepository;", "context", "Landroid/content/Context;", "(Lcom/zara/assistant/domain/repository/VoiceRepository;Lcom/zara/assistant/domain/repository/SettingsRepository;Landroid/content/Context;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/ui/viewmodel/MainUiState;", "sharedPreferences", "Landroid/content/SharedPreferences;", "getSharedPreferences", "()Landroid/content/SharedPreferences;", "sharedPreferences$delegate", "Lkotlin/Lazy;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "checkFirstLaunch", "", "completeOnboarding", "dismissError", "dismissPermissionDialog", "getVoiceStateText", "", "voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "observeSettings", "observeVoiceState", "onDestroy", "onPause", "onPermissionsDenied", "onPermissionsGranted", "onResume", "onSettingsClicked", "onVoiceButtonClicked", "onVoiceCommandIntent", "onWakeWordDetected", "onWakeWordServiceStarted", "onWakeWordToggle", "enabled", "", "refreshVoiceState", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setMissingPermissions", "permissions", "", "startListening", "stopListening", "stopSpeaking", "Companion", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.VoiceRepository voiceRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.SettingsRepository settingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.ui.viewmodel.MainUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.ui.viewmodel.MainUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy sharedPreferences$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_FIRST_LAUNCH = "is_first_launch";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_ONBOARDING_COMPLETED = "onboarding_completed";
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.ui.viewmodel.MainViewModel.Companion Companion = null;
    
    @javax.inject.Inject()
    public MainViewModel(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.VoiceRepository voiceRepository, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.SettingsRepository settingsRepository, @dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.zara.assistant.ui.viewmodel.MainUiState> getUiState() {
        return null;
    }
    
    private final android.content.SharedPreferences getSharedPreferences() {
        return null;
    }
    
    private final void observeVoiceState() {
    }
    
    private final void observeSettings() {
    }
    
    /**
     * Check if this is the first launch and update UI state accordingly
     */
    private final void checkFirstLaunch() {
    }
    
    /**
     * Mark onboarding as completed
     */
    public final void completeOnboarding() {
    }
    
    public final void onVoiceButtonClicked() {
    }
    
    public final void onSettingsClicked() {
    }
    
    public final void onPermissionsGranted() {
    }
    
    public final void onPermissionsDenied() {
    }
    
    public final void setMissingPermissions(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> permissions) {
    }
    
    public final void onWakeWordToggle(boolean enabled) {
    }
    
    public final void onWakeWordServiceStarted() {
    }
    
    public final void onVoiceCommandIntent() {
    }
    
    public final void onWakeWordDetected() {
    }
    
    public final void onResume() {
    }
    
    public final void onPause() {
    }
    
    public final void onDestroy() {
    }
    
    public final void dismissError() {
    }
    
    public final void dismissPermissionDialog() {
    }
    
    private final java.lang.Object startListening(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object stopListening(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object stopSpeaking(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object refreshVoiceState(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.String getVoiceStateText(com.zara.assistant.domain.model.VoiceState voiceState) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/zara/assistant/ui/viewmodel/MainViewModel$Companion;", "", "()V", "KEY_FIRST_LAUNCH", "", "KEY_ONBOARDING_COMPLETED", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}