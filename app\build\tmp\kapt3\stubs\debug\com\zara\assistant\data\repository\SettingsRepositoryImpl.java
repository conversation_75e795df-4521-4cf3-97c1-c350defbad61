package com.zara.assistant.data.repository;

/**
 * Implementation of SettingsRepository
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b/\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u001c\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000b\u0010\u0007J\u000e\u0010\f\u001a\u00020\rH\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\u000e\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0007J\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\n0\u0011H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\n0\u0011H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\u0013\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\u0015\u001a\u00020\nH\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\u0016\u001a\u00020\u0017H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\u0018\u001a\u00020\u0019H\u0096@\u00a2\u0006\u0002\u0010\u0007J$\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010\u001c\u001a\u00020\nH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u000e\u0010\u001f\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010 \u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u0016\u0010!\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020\nH\u0096@\u00a2\u0006\u0002\u0010\u001eJ\u000e\u0010#\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010$\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010%\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010&\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010\'\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0007J\u000e\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00170)H\u0016J\u001c\u0010*\u001a\b\u0012\u0004\u0012\u00020\u001b0\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b+\u0010\u0007J$\u0010,\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010-\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b.\u0010/J$\u00100\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00101\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u00103J$\u00104\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u00107J$\u00108\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b9\u00107J,\u0010:\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010\"\u001a\u00020\n2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b;\u0010<J$\u0010=\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b>\u00107J\u001c\u0010?\u001a\b\u0012\u0004\u0012\u00020\u001b0\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b@\u0010\u0007J$\u0010A\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bB\u00107J$\u0010C\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bD\u00107J$\u0010E\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010F\u001a\u00020\u0014H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bG\u0010HJ$\u0010I\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bJ\u00107J$\u0010K\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010L\u001a\u00020\nH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bM\u0010\u001eJ$\u0010N\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u00105\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bO\u00107J$\u0010P\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010Q\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bR\u0010SJ$\u0010T\u001a\b\u0012\u0004\u0012\u00020\u001b0\t2\u0006\u0010U\u001a\u00020\u0017H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bV\u0010WR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006X"}, d2 = {"Lcom/zara/assistant/data/repository/SettingsRepositoryImpl;", "Lcom/zara/assistant/domain/repository/SettingsRepository;", "preferencesManager", "Lcom/zara/assistant/data/local/preferences/PreferencesManager;", "(Lcom/zara/assistant/data/local/preferences/PreferencesManager;)V", "areNotificationsEnabled", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportSettings", "Lkotlin/Result;", "", "exportSettings-IoAF18A", "getAIPersonality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "getAIResponseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "getNotificationChannels", "", "getSupportedLanguages", "getThemeMode", "Lcom/zara/assistant/domain/repository/ThemeMode;", "getVoiceLanguage", "getVoiceSettings", "Lcom/zara/assistant/domain/model/VoiceSettings;", "getWakeWordSensitivity", "", "importSettings", "", "data", "importSettings-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isAccessibilityServiceEnabled", "isAnalyticsEnabled", "isChannelEnabled", "channelId", "isConversationHistoryEnabled", "isFirstLaunch", "isNotificationAccessEnabled", "isVoiceDataStorageEnabled", "isWakeWordEnabled", "observeVoiceSettings", "Lkotlinx/coroutines/flow/Flow;", "resetToDefaults", "resetToDefaults-IoAF18A", "setAIPersonality", "personality", "setAIPersonality-gIAlu-s", "(Lcom/zara/assistant/core/Constants$AIPersonality;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAIResponseStyle", "style", "setAIResponseStyle-gIAlu-s", "(Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAccessibilityServiceEnabled", "enabled", "setAccessibilityServiceEnabled-gIAlu-s", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAnalyticsEnabled", "setAnalyticsEnabled-gIAlu-s", "setChannelEnabled", "setChannelEnabled-0E7RQCE", "(Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setConversationHistoryEnabled", "setConversationHistoryEnabled-gIAlu-s", "setFirstLaunchCompleted", "setFirstLaunchCompleted-IoAF18A", "setNotificationAccessEnabled", "setNotificationAccessEnabled-gIAlu-s", "setNotificationsEnabled", "setNotificationsEnabled-gIAlu-s", "setThemeMode", "mode", "setThemeMode-gIAlu-s", "(Lcom/zara/assistant/domain/repository/ThemeMode;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setVoiceDataStorageEnabled", "setVoiceDataStorageEnabled-gIAlu-s", "setVoiceLanguage", "language", "setVoiceLanguage-gIAlu-s", "setWakeWordEnabled", "setWakeWordEnabled-gIAlu-s", "setWakeWordSensitivity", "sensitivity", "setWakeWordSensitivity-gIAlu-s", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVoiceSettings", "settings", "updateVoiceSettings-gIAlu-s", "(Lcom/zara/assistant/domain/model/VoiceSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SettingsRepositoryImpl implements com.zara.assistant.domain.repository.SettingsRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.data.local.preferences.PreferencesManager preferencesManager = null;
    
    @javax.inject.Inject()
    public SettingsRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.preferences.PreferencesManager preferencesManager) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getVoiceSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.VoiceSettings> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.VoiceSettings> observeVoiceSettings() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isWakeWordEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getWakeWordSensitivity(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Float> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAIPersonality(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.core.Constants.AIPersonality> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAIResponseStyle(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.core.Constants.AIResponseStyle> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isConversationHistoryEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isAnalyticsEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isVoiceDataStorageEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isAccessibilityServiceEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isNotificationAccessEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getVoiceLanguage(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getSupportedLanguages(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isFirstLaunch(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getThemeMode(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.repository.ThemeMode> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object areNotificationsEnabled(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getNotificationChannels(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isChannelEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String channelId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}