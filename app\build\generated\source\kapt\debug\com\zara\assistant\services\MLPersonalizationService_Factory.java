// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.data.local.dao.UserLearningDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MLPersonalizationService_Factory implements Factory<MLPersonalizationService> {
  private final Provider<Context> contextProvider;

  private final Provider<UserLearningDao> userLearningDaoProvider;

  public MLPersonalizationService_Factory(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider) {
    this.contextProvider = contextProvider;
    this.userLearningDaoProvider = userLearningDaoProvider;
  }

  @Override
  public MLPersonalizationService get() {
    return newInstance(contextProvider.get(), userLearningDaoProvider.get());
  }

  public static MLPersonalizationService_Factory create(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider) {
    return new MLPersonalizationService_Factory(contextProvider, userLearningDaoProvider);
  }

  public static MLPersonalizationService newInstance(Context context,
      UserLearningDao userLearningDao) {
    return new MLPersonalizationService(context, userLearningDao);
  }
}
