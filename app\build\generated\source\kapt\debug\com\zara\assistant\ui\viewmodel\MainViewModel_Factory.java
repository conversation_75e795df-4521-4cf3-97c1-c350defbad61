// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.ui.viewmodel;

import android.content.Context;
import com.zara.assistant.domain.repository.SettingsRepository;
import com.zara.assistant.domain.repository.VoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<VoiceRepository> voiceRepositoryProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  private final Provider<Context> contextProvider;

  public MainViewModel_Factory(Provider<VoiceRepository> voiceRepositoryProvider,
      Provider<SettingsRepository> settingsRepositoryProvider, Provider<Context> contextProvider) {
    this.voiceRepositoryProvider = voiceRepositoryProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(voiceRepositoryProvider.get(), settingsRepositoryProvider.get(), contextProvider.get());
  }

  public static MainViewModel_Factory create(Provider<VoiceRepository> voiceRepositoryProvider,
      Provider<SettingsRepository> settingsRepositoryProvider, Provider<Context> contextProvider) {
    return new MainViewModel_Factory(voiceRepositoryProvider, settingsRepositoryProvider, contextProvider);
  }

  public static MainViewModel newInstance(VoiceRepository voiceRepository,
      SettingsRepository settingsRepository, Context context) {
    return new MainViewModel(voiceRepository, settingsRepository, context);
  }
}
