// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import com.zara.assistant.data.local.dao.UserLearningDao;
import com.zara.assistant.data.local.database.ZaraDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideUserLearningDaoFactory implements Factory<UserLearningDao> {
  private final Provider<ZaraDatabase> databaseProvider;

  public DatabaseModule_ProvideUserLearningDaoFactory(Provider<ZaraDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public UserLearningDao get() {
    return provideUserLearningDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideUserLearningDaoFactory create(
      Provider<ZaraDatabase> databaseProvider) {
    return new DatabaseModule_ProvideUserLearningDaoFactory(databaseProvider);
  }

  public static UserLearningDao provideUserLearningDao(ZaraDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideUserLearningDao(database));
  }
}
