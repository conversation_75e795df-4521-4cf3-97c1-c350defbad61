// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LocalCommandProcessor_Factory implements Factory<LocalCommandProcessor> {
  private final Provider<Context> contextProvider;

  public LocalCommandProcessor_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LocalCommandProcessor get() {
    return newInstance(contextProvider.get());
  }

  public static LocalCommandProcessor_Factory create(Provider<Context> contextProvider) {
    return new LocalCommandProcessor_Factory(contextProvider);
  }

  public static LocalCommandProcessor newInstance(Context context) {
    return new LocalCommandProcessor(context);
  }
}
