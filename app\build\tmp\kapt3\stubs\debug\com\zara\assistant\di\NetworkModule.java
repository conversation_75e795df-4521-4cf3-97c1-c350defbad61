package com.zara.assistant.di;

/**
 * Dependency injection module for network-related dependencies
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u0007\u001a\u00020\bH\u0007J\u001a\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\b\b\u0001\u0010\r\u001a\u00020\bH\u0007J\u0012\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010\u000f\u001a\u00020\nH\u0007J\b\u0010\u0010\u001a\u00020\fH\u0007J\u0010\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0007J\u0012\u0010\u0012\u001a\u00020\u00132\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u0014\u001a\u00020\bH\u0007J\u001a\u0010\u0015\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\b\b\u0001\u0010\r\u001a\u00020\bH\u0007J\u0012\u0010\u0016\u001a\u00020\u00062\b\b\u0001\u0010\u000f\u001a\u00020\nH\u0007\u00a8\u0006\u0017"}, d2 = {"Lcom/zara/assistant/di/NetworkModule;", "", "()V", "provideCohereApiService", "Lcom/zara/assistant/data/remote/api/CohereApiService;", "retrofit", "Lretrofit2/Retrofit;", "provideCohereAuthInterceptor", "Lcom/zara/assistant/data/remote/interceptor/AuthInterceptor;", "provideCohereOkHttpClient", "Lokhttp3/OkHttpClient;", "loggingInterceptor", "Lokhttp3/logging/HttpLoggingInterceptor;", "authInterceptor", "provideCohereRetrofit", "okHttpClient", "provideHttpLoggingInterceptor", "provideOkHttpClient", "providePerplexityApiService", "Lcom/zara/assistant/data/remote/api/PerplexityApiService;", "providePerplexityAuthInterceptor", "providePerplexityOkHttpClient", "providePerplexityRetrofit", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class NetworkModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.di.NetworkModule INSTANCE = null;
    
    private NetworkModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.logging.HttpLoggingInterceptor provideHttpLoggingInterceptor() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "cohere_auth")
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.remote.interceptor.AuthInterceptor provideCohereAuthInterceptor() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "perplexity_auth")
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.remote.interceptor.AuthInterceptor providePerplexityAuthInterceptor() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient provideOkHttpClient(@org.jetbrains.annotations.NotNull()
    okhttp3.logging.HttpLoggingInterceptor loggingInterceptor) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "cohere_client")
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient provideCohereOkHttpClient(@org.jetbrains.annotations.NotNull()
    okhttp3.logging.HttpLoggingInterceptor loggingInterceptor, @javax.inject.Named(value = "cohere_auth")
    @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.interceptor.AuthInterceptor authInterceptor) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "perplexity_client")
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient providePerplexityOkHttpClient(@org.jetbrains.annotations.NotNull()
    okhttp3.logging.HttpLoggingInterceptor loggingInterceptor, @javax.inject.Named(value = "perplexity_auth")
    @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.interceptor.AuthInterceptor authInterceptor) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "cohere_retrofit")
    @org.jetbrains.annotations.NotNull()
    public final retrofit2.Retrofit provideCohereRetrofit(@javax.inject.Named(value = "cohere_client")
    @org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "perplexity_retrofit")
    @org.jetbrains.annotations.NotNull()
    public final retrofit2.Retrofit providePerplexityRetrofit(@javax.inject.Named(value = "perplexity_client")
    @org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.remote.api.CohereApiService provideCohereApiService(@javax.inject.Named(value = "cohere_retrofit")
    @org.jetbrains.annotations.NotNull()
    retrofit2.Retrofit retrofit) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.remote.api.PerplexityApiService providePerplexityApiService(@javax.inject.Named(value = "perplexity_retrofit")
    @org.jetbrains.annotations.NotNull()
    retrofit2.Retrofit retrofit) {
        return null;
    }
}