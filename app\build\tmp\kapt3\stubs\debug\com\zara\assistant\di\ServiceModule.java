package com.zara.assistant.di;

/**
 * Dependency injection module for service-related dependencies
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0007J\u001a\u0010\r\u001a\u00020\n2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u000fH\u0007J\u001a\u0010\u0010\u001a\u00020\b2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u000fH\u0007J\"\u0010\u0011\u001a\u00020\f2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u0013H\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/zara/assistant/di/ServiceModule;", "", "()V", "provideAIOrchestrationService", "Lcom/zara/assistant/services/AIOrchestrationService;", "context", "Landroid/content/Context;", "personalMemoryService", "Lcom/zara/assistant/services/PersonalMemoryService;", "mlPersonalizationService", "Lcom/zara/assistant/services/MLPersonalizationService;", "webSearchService", "Lcom/zara/assistant/services/WebSearchService;", "provideMLPersonalizationService", "userLearningDao", "Lcom/zara/assistant/data/local/dao/UserLearningDao;", "providePersonalMemoryService", "provideWebSearchService", "okHttpClient", "Lokhttp3/OkHttpClient;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class ServiceModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.di.ServiceModule INSTANCE = null;
    
    private ServiceModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.PersonalMemoryService providePersonalMemoryService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.dao.UserLearningDao userLearningDao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.MLPersonalizationService provideMLPersonalizationService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.dao.UserLearningDao userLearningDao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.WebSearchService provideWebSearchService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.dao.UserLearningDao userLearningDao, @org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.services.AIOrchestrationService provideAIOrchestrationService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.PersonalMemoryService personalMemoryService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.MLPersonalizationService mlPersonalizationService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.services.WebSearchService webSearchService) {
        return null;
    }
}