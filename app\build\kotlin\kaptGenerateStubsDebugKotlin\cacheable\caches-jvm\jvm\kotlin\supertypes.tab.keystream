"com.zara.assistant.ZaraApplication,com.zara.assistant.core.Constants.VoiceState/com.zara.assistant.core.Constants.AIPersonality1com.zara.assistant.core.Constants.AIResponseStyle3com.zara.assistant.data.local.database.ZaraDatabase:com.zara.assistant.data.remote.interceptor.AuthInterceptor3com.zara.assistant.data.repository.AIRepositoryImpl=com.zara.assistant.data.repository.ConversationRepositoryImpl9com.zara.assistant.data.repository.SettingsRepositoryImpl6com.zara.assistant.data.repository.VoiceRepositoryImpl*com.zara.assistant.domain.model.AIResponse(com.zara.assistant.domain.model.AISource,com.zara.assistant.domain.model.Conversation3com.zara.assistant.domain.model.ConversationMessage-com.zara.assistant.domain.model.MessageSender+com.zara.assistant.domain.model.MessageType1com.zara.assistant.domain.model.ConversationState0com.zara.assistant.domain.model.ConversationType7com.zara.assistant.domain.model.ConversationSessionType,com.zara.assistant.domain.model.SystemAction*com.zara.assistant.domain.model.ActionType,com.zara.assistant.domain.model.VoiceCommand+com.zara.assistant.domain.model.CommandType*com.zara.assistant.domain.model.VoiceState0com.zara.assistant.domain.model.VoiceState.State-com.zara.assistant.domain.model.VoiceSettings+com.zara.assistant.domain.model.AudioConfig.com.zara.assistant.domain.repository.ThemeMode/com.zara.assistant.services.AIProcessingServiceBcom.zara.assistant.services.AIProcessingService.AIProcessingBinder:com.zara.assistant.services.AdvancedVoiceProcessingServicePcom.zara.assistant.services.AdvancedVoiceProcessingService.VoiceProcessingBinder<com.zara.assistant.services.LocalCommandResult.SystemControl9com.zara.assistant.services.LocalCommandResult.AppControl<com.zara.assistant.services.LocalCommandResult.QuickResponse9com.zara.assistant.services.LocalCommandResult.RequiresAI7com.zara.assistant.services.NotificationListenerService/com.zara.assistant.services.UserLearningService+com.zara.assistant.services.WakeWordService:com.zara.assistant.services.WakeWordService.WakeWordBinder4com.zara.assistant.services.ZaraAccessibilityService3com.zara.assistant.services.ZaraDeviceAdminReceiver"com.zara.assistant.ui.MainActivity5com.zara.assistant.ui.RestrictedSettingsGuideActivity0com.zara.assistant.ui.screens.SettingItem.Toggle0com.zara.assistant.ui.screens.SettingItem.Slider0com.zara.assistant.ui.screens.SettingItem.Action-com.zara.assistant.ui.viewmodel.MainViewModel1com.zara.assistant.ui.viewmodel.SettingsViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           