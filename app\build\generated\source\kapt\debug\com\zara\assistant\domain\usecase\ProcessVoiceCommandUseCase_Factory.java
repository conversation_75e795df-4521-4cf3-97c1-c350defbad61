// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.domain.usecase;

import com.zara.assistant.domain.repository.AIRepository;
import com.zara.assistant.domain.repository.ConversationRepository;
import com.zara.assistant.domain.repository.VoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProcessVoiceCommandUseCase_Factory implements Factory<ProcessVoiceCommandUseCase> {
  private final Provider<VoiceRepository> voiceRepositoryProvider;

  private final Provider<AIRepository> aiRepositoryProvider;

  private final Provider<ConversationRepository> conversationRepositoryProvider;

  public ProcessVoiceCommandUseCase_Factory(Provider<VoiceRepository> voiceRepositoryProvider,
      Provider<AIRepository> aiRepositoryProvider,
      Provider<ConversationRepository> conversationRepositoryProvider) {
    this.voiceRepositoryProvider = voiceRepositoryProvider;
    this.aiRepositoryProvider = aiRepositoryProvider;
    this.conversationRepositoryProvider = conversationRepositoryProvider;
  }

  @Override
  public ProcessVoiceCommandUseCase get() {
    return newInstance(voiceRepositoryProvider.get(), aiRepositoryProvider.get(), conversationRepositoryProvider.get());
  }

  public static ProcessVoiceCommandUseCase_Factory create(
      Provider<VoiceRepository> voiceRepositoryProvider,
      Provider<AIRepository> aiRepositoryProvider,
      Provider<ConversationRepository> conversationRepositoryProvider) {
    return new ProcessVoiceCommandUseCase_Factory(voiceRepositoryProvider, aiRepositoryProvider, conversationRepositoryProvider);
  }

  public static ProcessVoiceCommandUseCase newInstance(VoiceRepository voiceRepository,
      AIRepository aiRepository, ConversationRepository conversationRepository) {
    return new ProcessVoiceCommandUseCase(voiceRepository, aiRepository, conversationRepository);
  }
}
