{"logs": [{"outputFile": "com.zara.assistant.app-mergeReleaseResources-65:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecb7be72ebd258ebb899bf8355eead54\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "36,37,38,39,40,41,42,99,100,101,102,103,104,106,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3574,3673,3761,3861,3961,4048,4127,10521,10613,10700,10771,10839,10920,11092,11269,11347,11416", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "3668,3756,3856,3956,4043,4122,4214,10608,10695,10766,10834,10915,11000,11163,11342,11411,11533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1876129b9e490c2861a5ecf91d698967\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "11538,11627", "endColumns": "88,94", "endOffsets": "11622,11717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac2f50916fa650919f030349484e55e3\\transformed\\jetified-material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4759,4845,4952,5032,5117,5219,5331,5429,5529,5617,5733,5834,5937,6069,6149,6259", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4754,4840,4947,5027,5112,5214,5326,5424,5524,5612,5728,5829,5932,6064,6144,6254,6352"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4219,4339,4457,4579,4700,4799,4893,5005,5149,5268,5415,5499,5599,5700,5801,5922,6049,6154,6304,6450,6580,6772,6898,7016,7139,7272,7374,7479,7603,7728,7830,7937,8042,8187,8339,8448,8557,8644,8737,8832,8923,9009,9116,9196,9281,9383,9495,9593,9693,9781,9897,9998,10101,10233,10313,10423", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "4334,4452,4574,4695,4794,4888,5000,5144,5263,5410,5494,5594,5695,5796,5917,6044,6149,6299,6445,6575,6767,6893,7011,7134,7267,7369,7474,7598,7723,7825,7932,8037,8182,8334,8443,8552,8639,8732,8827,8918,9004,9111,9191,9276,9378,9490,9588,9688,9776,9892,9993,10096,10228,10308,10418,10516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5e0eb68a5716cdfe313e221efb4d1df6\\transformed\\core-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2847,2945,3047,3146,3248,3352,3456,11168", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "2940,3042,3141,3243,3347,3451,3569,11264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47494bae0d5af340ce384dde3add152e\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,11005", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,11087"}}]}]}