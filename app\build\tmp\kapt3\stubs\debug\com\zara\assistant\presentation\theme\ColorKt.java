package com.zara.assistant.presentation.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0013\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010 \n\u0003\b\u00bf\u0001\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010 \u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"\"\u0017\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010 \u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\"\"\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010 \u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\"\"\u0017\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00010 \u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\"\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\"\u0013\u0010?\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b@\u0010\u0003\"\u0013\u0010A\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bB\u0010\u0003\"\u0013\u0010C\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bD\u0010\u0003\"\u0013\u0010E\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bF\u0010\u0003\"\u0013\u0010G\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bH\u0010\u0003\"\u0013\u0010I\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bJ\u0010\u0003\"\u0013\u0010K\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bL\u0010\u0003\"\u0013\u0010M\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bN\u0010\u0003\"\u0013\u0010O\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bP\u0010\u0003\"\u0013\u0010Q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bR\u0010\u0003\"\u0013\u0010S\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bT\u0010\u0003\"\u0013\u0010U\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bV\u0010\u0003\"\u0013\u0010W\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bX\u0010\u0003\"\u0013\u0010Y\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bZ\u0010\u0003\"\u0013\u0010[\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\\\u0010\u0003\"\u0013\u0010]\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b^\u0010\u0003\"\u0013\u0010_\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b`\u0010\u0003\"\u0013\u0010a\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bb\u0010\u0003\"\u0013\u0010c\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bd\u0010\u0003\"\u0013\u0010e\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bf\u0010\u0003\"\u0013\u0010g\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bh\u0010\u0003\"\u0013\u0010i\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bj\u0010\u0003\"\u0013\u0010k\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bl\u0010\u0003\"\u0013\u0010m\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bn\u0010\u0003\"\u0013\u0010o\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bp\u0010\u0003\"\u0013\u0010q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\br\u0010\u0003\"\u0013\u0010s\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bt\u0010\u0003\"\u0013\u0010u\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bv\u0010\u0003\"\u0013\u0010w\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bx\u0010\u0003\"\u0013\u0010y\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bz\u0010\u0003\"\u0013\u0010{\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b|\u0010\u0003\"\u0013\u0010}\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b~\u0010\u0003\"\u0014\u0010\u007f\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0080\u0001\u0010\u0003\"\u0015\u0010\u0081\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0082\u0001\u0010\u0003\"\u0015\u0010\u0083\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0084\u0001\u0010\u0003\"\u0015\u0010\u0085\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0086\u0001\u0010\u0003\"\u0015\u0010\u0087\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0088\u0001\u0010\u0003\"\u0015\u0010\u0089\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u008a\u0001\u0010\u0003\"\u0015\u0010\u008b\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u008c\u0001\u0010\u0003\"\u0015\u0010\u008d\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u008e\u0001\u0010\u0003\"\u0015\u0010\u008f\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0090\u0001\u0010\u0003\"\u0015\u0010\u0091\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0092\u0001\u0010\u0003\"\u0015\u0010\u0093\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0094\u0001\u0010\u0003\"\u0015\u0010\u0095\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0096\u0001\u0010\u0003\"\u0015\u0010\u0097\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0098\u0001\u0010\u0003\"\u0015\u0010\u0099\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u009a\u0001\u0010\u0003\"\u0015\u0010\u009b\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u009c\u0001\u0010\u0003\"\u0015\u0010\u009d\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u009e\u0001\u0010\u0003\"\u0015\u0010\u009f\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00a0\u0001\u0010\u0003\"\u0015\u0010\u00a1\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00a2\u0001\u0010\u0003\"\u0015\u0010\u00a3\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00a4\u0001\u0010\u0003\"\u0015\u0010\u00a5\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00a6\u0001\u0010\u0003\"\u0015\u0010\u00a7\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00a8\u0001\u0010\u0003\"\u0015\u0010\u00a9\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00aa\u0001\u0010\u0003\"\u0015\u0010\u00ab\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00ac\u0001\u0010\u0003\"\u0015\u0010\u00ad\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00ae\u0001\u0010\u0003\"\u0015\u0010\u00af\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00b0\u0001\u0010\u0003\"\u0015\u0010\u00b1\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00b2\u0001\u0010\u0003\"\u0015\u0010\u00b3\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00b4\u0001\u0010\u0003\"\u0015\u0010\u00b5\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00b6\u0001\u0010\u0003\"\u0015\u0010\u00b7\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00b8\u0001\u0010\u0003\"\u0015\u0010\u00b9\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00ba\u0001\u0010\u0003\"\u0015\u0010\u00bb\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00bc\u0001\u0010\u0003\"\u0015\u0010\u00bd\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00be\u0001\u0010\u0003\"\u0015\u0010\u00bf\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00c0\u0001\u0010\u0003\"\u0015\u0010\u00c1\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00c2\u0001\u0010\u0003\"\u0015\u0010\u00c3\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00c4\u0001\u0010\u0003\"\u0015\u0010\u00c5\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00c6\u0001\u0010\u0003\"\u0015\u0010\u00c7\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00c8\u0001\u0010\u0003\"\u0015\u0010\u00c9\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00ca\u0001\u0010\u0003\"\u0015\u0010\u00cb\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00cc\u0001\u0010\u0003\"\u0015\u0010\u00cd\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00ce\u0001\u0010\u0003\"\u0015\u0010\u00cf\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00d0\u0001\u0010\u0003\"\u0015\u0010\u00d1\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00d2\u0001\u0010\u0003\"\u0015\u0010\u00d3\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00d4\u0001\u0010\u0003\"\u0015\u0010\u00d5\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00d6\u0001\u0010\u0003\"\u0015\u0010\u00d7\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00d8\u0001\u0010\u0003\"\u0015\u0010\u00d9\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00da\u0001\u0010\u0003\"\u0015\u0010\u00db\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00dc\u0001\u0010\u0003\"\u0015\u0010\u00dd\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u00de\u0001\u0010\u0003\u00a8\u0006\u00df\u0001"}, d2 = {"AccentError", "Landroidx/compose/ui/graphics/Color;", "getAccentError", "()J", "J", "AccentErrorLight", "getAccentErrorLight", "AccentInfo", "getAccentInfo", "AccentInfoLight", "getAccentInfoLight", "AccentSuccess", "getAccentSuccess", "AccentSuccessLight", "getAccentSuccessLight", "AccentWarning", "getAccentWarning", "AccentWarningLight", "getAccentWarningLight", "DeepTeal", "getDeepTeal", "DeepTealVariant", "getDeepTealVariant", "ElectricPurple", "getElectricPurple", "ElectricPurpleVariant", "getElectricPurpleVariant", "EmeraldGreen", "getEmeraldGreen", "GoldenAmber", "getGoldenAmber", "GradientAccent", "", "getGradientAccent", "()Ljava/util/List;", "GradientNeutral", "getGradientNeutral", "GradientPrimary", "getGradientPrimary", "GradientSecondary", "getGradientSecondary", "LuminousBlue", "getLuminousBlue", "LuminousBlueVariant", "getLuminousBlueVariant", "NeuroBackgroundDark", "getNeuroBackgroundDark", "NeuroBackgroundLight", "getNeuroBackgroundLight", "NeuroBackgroundMedium", "getNeuroBackgroundMedium", "NeuroDarkHighlight", "getNeuroDarkHighlight", "NeuroDarkShadow", "getNeuroDarkShadow", "NeuroLightHighlight", "getNeuroLightHighlight", "NeuroLightShadow", "getNeuroLightShadow", "NeuroSurfaceDark", "getNeuroSurfaceDark", "NeuroSurfaceDarkElevated", "getNeuroSurfaceDarkElevated", "NeuroSurfaceLight", "getNeuroSurfaceLight", "NeuroSurfaceLightElevated", "getNeuroSurfaceLightElevated", "SoftCoral", "getSoftCoral", "SoftCoralVariant", "getSoftCoralVariant", "TextDisabledDark", "getTextDisabledDark", "TextDisabledLight", "getTextDisabledLight", "TextPrimaryDark", "getTextPrimaryDark", "TextPrimaryLight", "getTextPrimaryLight", "TextSecondaryDark", "getTextSecondaryDark", "TextSecondaryLight", "getTextSecondaryLight", "TextTertiaryDark", "getTextTertiaryDark", "TextTertiaryLight", "getTextTertiaryLight", "VoiceError", "getVoiceError", "VoiceIdle", "getVoiceIdle", "VoiceListening", "getVoiceListening", "VoiceListeningGlow", "getVoiceListeningGlow", "VoiceProcessing", "getVoiceProcessing", "VoiceProcessingGlow", "getVoiceProcessingGlow", "VoiceSpeaking", "getVoiceSpeaking", "VoiceSpeakingGlow", "getVoiceSpeakingGlow", "md_theme_dark_background", "getMd_theme_dark_background", "md_theme_dark_error", "getMd_theme_dark_error", "md_theme_dark_errorContainer", "getMd_theme_dark_errorContainer", "md_theme_dark_inverseOnSurface", "getMd_theme_dark_inverseOnSurface", "md_theme_dark_inversePrimary", "getMd_theme_dark_inversePrimary", "md_theme_dark_inverseSurface", "getMd_theme_dark_inverseSurface", "md_theme_dark_onBackground", "getMd_theme_dark_onBackground", "md_theme_dark_onError", "getMd_theme_dark_onError", "md_theme_dark_onErrorContainer", "getMd_theme_dark_onErrorContainer", "md_theme_dark_onPrimary", "getMd_theme_dark_onPrimary", "md_theme_dark_onPrimaryContainer", "getMd_theme_dark_onPrimaryContainer", "md_theme_dark_onSecondary", "getMd_theme_dark_onSecondary", "md_theme_dark_onSecondaryContainer", "getMd_theme_dark_onSecondaryContainer", "md_theme_dark_onSurface", "getMd_theme_dark_onSurface", "md_theme_dark_onSurfaceVariant", "getMd_theme_dark_onSurfaceVariant", "md_theme_dark_onTertiary", "getMd_theme_dark_onTertiary", "md_theme_dark_onTertiaryContainer", "getMd_theme_dark_onTertiaryContainer", "md_theme_dark_outline", "getMd_theme_dark_outline", "md_theme_dark_outlineVariant", "getMd_theme_dark_outlineVariant", "md_theme_dark_primary", "getMd_theme_dark_primary", "md_theme_dark_primaryContainer", "getMd_theme_dark_primaryContainer", "md_theme_dark_scrim", "getMd_theme_dark_scrim", "md_theme_dark_secondary", "getMd_theme_dark_secondary", "md_theme_dark_secondaryContainer", "getMd_theme_dark_secondaryContainer", "md_theme_dark_shadow", "getMd_theme_dark_shadow", "md_theme_dark_surface", "getMd_theme_dark_surface", "md_theme_dark_surfaceTint", "getMd_theme_dark_surfaceTint", "md_theme_dark_surfaceVariant", "getMd_theme_dark_surfaceVariant", "md_theme_dark_tertiary", "getMd_theme_dark_tertiary", "md_theme_dark_tertiaryContainer", "getMd_theme_dark_tertiaryContainer", "md_theme_light_background", "getMd_theme_light_background", "md_theme_light_error", "getMd_theme_light_error", "md_theme_light_errorContainer", "getMd_theme_light_errorContainer", "md_theme_light_inverseOnSurface", "getMd_theme_light_inverseOnSurface", "md_theme_light_inversePrimary", "getMd_theme_light_inversePrimary", "md_theme_light_inverseSurface", "getMd_theme_light_inverseSurface", "md_theme_light_onBackground", "getMd_theme_light_onBackground", "md_theme_light_onError", "getMd_theme_light_onError", "md_theme_light_onErrorContainer", "getMd_theme_light_onErrorContainer", "md_theme_light_onPrimary", "getMd_theme_light_onPrimary", "md_theme_light_onPrimaryContainer", "getMd_theme_light_onPrimaryContainer", "md_theme_light_onSecondary", "getMd_theme_light_onSecondary", "md_theme_light_onSecondaryContainer", "getMd_theme_light_onSecondaryContainer", "md_theme_light_onSurface", "getMd_theme_light_onSurface", "md_theme_light_onSurfaceVariant", "getMd_theme_light_onSurfaceVariant", "md_theme_light_onTertiary", "getMd_theme_light_onTertiary", "md_theme_light_onTertiaryContainer", "getMd_theme_light_onTertiaryContainer", "md_theme_light_outline", "getMd_theme_light_outline", "md_theme_light_outlineVariant", "getMd_theme_light_outlineVariant", "md_theme_light_primary", "getMd_theme_light_primary", "md_theme_light_primaryContainer", "getMd_theme_light_primaryContainer", "md_theme_light_scrim", "getMd_theme_light_scrim", "md_theme_light_secondary", "getMd_theme_light_secondary", "md_theme_light_secondaryContainer", "getMd_theme_light_secondaryContainer", "md_theme_light_shadow", "getMd_theme_light_shadow", "md_theme_light_surface", "getMd_theme_light_surface", "md_theme_light_surfaceTint", "getMd_theme_light_surfaceTint", "md_theme_light_surfaceVariant", "getMd_theme_light_surfaceVariant", "md_theme_light_tertiary", "getMd_theme_light_tertiary", "md_theme_light_tertiaryContainer", "getMd_theme_light_tertiaryContainer", "app_debug"})
public final class ColorKt {
    private static final long DeepTeal = 0L;
    private static final long SoftCoral = 0L;
    private static final long LuminousBlue = 0L;
    private static final long ElectricPurple = 0L;
    private static final long GoldenAmber = 0L;
    private static final long EmeraldGreen = 0L;
    private static final long DeepTealVariant = 0L;
    private static final long SoftCoralVariant = 0L;
    private static final long LuminousBlueVariant = 0L;
    private static final long ElectricPurpleVariant = 0L;
    private static final long NeuroBackgroundLight = 0L;
    private static final long NeuroBackgroundDark = 0L;
    private static final long NeuroBackgroundMedium = 0L;
    private static final long NeuroLightShadow = 0L;
    private static final long NeuroLightHighlight = 0L;
    private static final long NeuroSurfaceLight = 0L;
    private static final long NeuroSurfaceLightElevated = 0L;
    private static final long NeuroDarkShadow = 0L;
    private static final long NeuroDarkHighlight = 0L;
    private static final long NeuroSurfaceDark = 0L;
    private static final long NeuroSurfaceDarkElevated = 0L;
    private static final long TextPrimaryLight = 0L;
    private static final long TextSecondaryLight = 0L;
    private static final long TextTertiaryLight = 0L;
    private static final long TextDisabledLight = 0L;
    private static final long TextPrimaryDark = 0L;
    private static final long TextSecondaryDark = 0L;
    private static final long TextTertiaryDark = 0L;
    private static final long TextDisabledDark = 0L;
    private static final long AccentSuccess = 0L;
    private static final long AccentSuccessLight = 0L;
    private static final long AccentWarning = 0L;
    private static final long AccentWarningLight = 0L;
    private static final long AccentError = 0L;
    private static final long AccentErrorLight = 0L;
    private static final long AccentInfo = 0L;
    private static final long AccentInfoLight = 0L;
    private static final long VoiceListening = 0L;
    private static final long VoiceListeningGlow = 0L;
    private static final long VoiceProcessing = 0L;
    private static final long VoiceProcessingGlow = 0L;
    private static final long VoiceSpeaking = 0L;
    private static final long VoiceSpeakingGlow = 0L;
    private static final long VoiceIdle = 0L;
    private static final long VoiceError = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> GradientPrimary = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> GradientSecondary = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> GradientAccent = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> GradientNeutral = null;
    private static final long md_theme_light_primary = 0L;
    private static final long md_theme_light_onPrimary = 0L;
    private static final long md_theme_light_primaryContainer = 0L;
    private static final long md_theme_light_onPrimaryContainer = 0L;
    private static final long md_theme_light_secondary = 0L;
    private static final long md_theme_light_onSecondary = 0L;
    private static final long md_theme_light_secondaryContainer = 0L;
    private static final long md_theme_light_onSecondaryContainer = 0L;
    private static final long md_theme_light_tertiary = 0L;
    private static final long md_theme_light_onTertiary = 0L;
    private static final long md_theme_light_tertiaryContainer = 0L;
    private static final long md_theme_light_onTertiaryContainer = 0L;
    private static final long md_theme_light_error = 0L;
    private static final long md_theme_light_errorContainer = 0L;
    private static final long md_theme_light_onError = 0L;
    private static final long md_theme_light_onErrorContainer = 0L;
    private static final long md_theme_light_background = 0L;
    private static final long md_theme_light_onBackground = 0L;
    private static final long md_theme_light_surface = 0L;
    private static final long md_theme_light_onSurface = 0L;
    private static final long md_theme_light_surfaceVariant = 0L;
    private static final long md_theme_light_onSurfaceVariant = 0L;
    private static final long md_theme_light_outline = 0L;
    private static final long md_theme_light_inverseOnSurface = 0L;
    private static final long md_theme_light_inverseSurface = 0L;
    private static final long md_theme_light_inversePrimary = 0L;
    private static final long md_theme_light_shadow = 0L;
    private static final long md_theme_light_surfaceTint = 0L;
    private static final long md_theme_light_outlineVariant = 0L;
    private static final long md_theme_light_scrim = 0L;
    private static final long md_theme_dark_primary = 0L;
    private static final long md_theme_dark_onPrimary = 0L;
    private static final long md_theme_dark_primaryContainer = 0L;
    private static final long md_theme_dark_onPrimaryContainer = 0L;
    private static final long md_theme_dark_secondary = 0L;
    private static final long md_theme_dark_onSecondary = 0L;
    private static final long md_theme_dark_secondaryContainer = 0L;
    private static final long md_theme_dark_onSecondaryContainer = 0L;
    private static final long md_theme_dark_tertiary = 0L;
    private static final long md_theme_dark_onTertiary = 0L;
    private static final long md_theme_dark_tertiaryContainer = 0L;
    private static final long md_theme_dark_onTertiaryContainer = 0L;
    private static final long md_theme_dark_error = 0L;
    private static final long md_theme_dark_errorContainer = 0L;
    private static final long md_theme_dark_onError = 0L;
    private static final long md_theme_dark_onErrorContainer = 0L;
    private static final long md_theme_dark_background = 0L;
    private static final long md_theme_dark_onBackground = 0L;
    private static final long md_theme_dark_surface = 0L;
    private static final long md_theme_dark_onSurface = 0L;
    private static final long md_theme_dark_surfaceVariant = 0L;
    private static final long md_theme_dark_onSurfaceVariant = 0L;
    private static final long md_theme_dark_outline = 0L;
    private static final long md_theme_dark_inverseOnSurface = 0L;
    private static final long md_theme_dark_inverseSurface = 0L;
    private static final long md_theme_dark_inversePrimary = 0L;
    private static final long md_theme_dark_shadow = 0L;
    private static final long md_theme_dark_surfaceTint = 0L;
    private static final long md_theme_dark_outlineVariant = 0L;
    private static final long md_theme_dark_scrim = 0L;
    
    public static final long getDeepTeal() {
        return 0L;
    }
    
    public static final long getSoftCoral() {
        return 0L;
    }
    
    public static final long getLuminousBlue() {
        return 0L;
    }
    
    public static final long getElectricPurple() {
        return 0L;
    }
    
    public static final long getGoldenAmber() {
        return 0L;
    }
    
    public static final long getEmeraldGreen() {
        return 0L;
    }
    
    public static final long getDeepTealVariant() {
        return 0L;
    }
    
    public static final long getSoftCoralVariant() {
        return 0L;
    }
    
    public static final long getLuminousBlueVariant() {
        return 0L;
    }
    
    public static final long getElectricPurpleVariant() {
        return 0L;
    }
    
    public static final long getNeuroBackgroundLight() {
        return 0L;
    }
    
    public static final long getNeuroBackgroundDark() {
        return 0L;
    }
    
    public static final long getNeuroBackgroundMedium() {
        return 0L;
    }
    
    public static final long getNeuroLightShadow() {
        return 0L;
    }
    
    public static final long getNeuroLightHighlight() {
        return 0L;
    }
    
    public static final long getNeuroSurfaceLight() {
        return 0L;
    }
    
    public static final long getNeuroSurfaceLightElevated() {
        return 0L;
    }
    
    public static final long getNeuroDarkShadow() {
        return 0L;
    }
    
    public static final long getNeuroDarkHighlight() {
        return 0L;
    }
    
    public static final long getNeuroSurfaceDark() {
        return 0L;
    }
    
    public static final long getNeuroSurfaceDarkElevated() {
        return 0L;
    }
    
    public static final long getTextPrimaryLight() {
        return 0L;
    }
    
    public static final long getTextSecondaryLight() {
        return 0L;
    }
    
    public static final long getTextTertiaryLight() {
        return 0L;
    }
    
    public static final long getTextDisabledLight() {
        return 0L;
    }
    
    public static final long getTextPrimaryDark() {
        return 0L;
    }
    
    public static final long getTextSecondaryDark() {
        return 0L;
    }
    
    public static final long getTextTertiaryDark() {
        return 0L;
    }
    
    public static final long getTextDisabledDark() {
        return 0L;
    }
    
    public static final long getAccentSuccess() {
        return 0L;
    }
    
    public static final long getAccentSuccessLight() {
        return 0L;
    }
    
    public static final long getAccentWarning() {
        return 0L;
    }
    
    public static final long getAccentWarningLight() {
        return 0L;
    }
    
    public static final long getAccentError() {
        return 0L;
    }
    
    public static final long getAccentErrorLight() {
        return 0L;
    }
    
    public static final long getAccentInfo() {
        return 0L;
    }
    
    public static final long getAccentInfoLight() {
        return 0L;
    }
    
    public static final long getVoiceListening() {
        return 0L;
    }
    
    public static final long getVoiceListeningGlow() {
        return 0L;
    }
    
    public static final long getVoiceProcessing() {
        return 0L;
    }
    
    public static final long getVoiceProcessingGlow() {
        return 0L;
    }
    
    public static final long getVoiceSpeaking() {
        return 0L;
    }
    
    public static final long getVoiceSpeakingGlow() {
        return 0L;
    }
    
    public static final long getVoiceIdle() {
        return 0L;
    }
    
    public static final long getVoiceError() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getGradientPrimary() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getGradientSecondary() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getGradientAccent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getGradientNeutral() {
        return null;
    }
    
    public static final long getMd_theme_light_primary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onPrimary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_primaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onPrimaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_secondary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onSecondary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_secondaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onSecondaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_tertiary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onTertiary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_tertiaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onTertiaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_error() {
        return 0L;
    }
    
    public static final long getMd_theme_light_errorContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onError() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onErrorContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_light_background() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onBackground() {
        return 0L;
    }
    
    public static final long getMd_theme_light_surface() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onSurface() {
        return 0L;
    }
    
    public static final long getMd_theme_light_surfaceVariant() {
        return 0L;
    }
    
    public static final long getMd_theme_light_onSurfaceVariant() {
        return 0L;
    }
    
    public static final long getMd_theme_light_outline() {
        return 0L;
    }
    
    public static final long getMd_theme_light_inverseOnSurface() {
        return 0L;
    }
    
    public static final long getMd_theme_light_inverseSurface() {
        return 0L;
    }
    
    public static final long getMd_theme_light_inversePrimary() {
        return 0L;
    }
    
    public static final long getMd_theme_light_shadow() {
        return 0L;
    }
    
    public static final long getMd_theme_light_surfaceTint() {
        return 0L;
    }
    
    public static final long getMd_theme_light_outlineVariant() {
        return 0L;
    }
    
    public static final long getMd_theme_light_scrim() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_primary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onPrimary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_primaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onPrimaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_secondary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onSecondary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_secondaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onSecondaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_tertiary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onTertiary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_tertiaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onTertiaryContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_error() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_errorContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onError() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onErrorContainer() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_background() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onBackground() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_surface() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onSurface() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_surfaceVariant() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_onSurfaceVariant() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_outline() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_inverseOnSurface() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_inverseSurface() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_inversePrimary() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_shadow() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_surfaceTint() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_outlineVariant() {
        return 0L;
    }
    
    public static final long getMd_theme_dark_scrim() {
        return 0L;
    }
}