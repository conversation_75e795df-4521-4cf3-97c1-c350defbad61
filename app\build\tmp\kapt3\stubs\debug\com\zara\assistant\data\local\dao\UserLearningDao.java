package com.zara.assistant.data.local.dao;

/**
 * Data Access Object for user learning data
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b$\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\n\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0018\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u001a\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u001c\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\u0006\u0010\u001f\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u000f2\u0006\u0010\"\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020!0\u000f2\u0006\u0010$\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\u000f2\u0006\u0010\'\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00130\u000f2\u0006\u0010)\u001a\u00020*H\u00a7@\u00a2\u0006\u0002\u0010+J\u0016\u0010,\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010-\u001a\b\u0012\u0004\u0012\u00020.0\u000f2\u0006\u0010/\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u001c\u00101\u001a\b\u0012\u0004\u0012\u00020.0\u000f2\u0006\u00102\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J$\u00103\u001a\b\u0012\u0004\u0012\u00020.0\u000f2\u0006\u0010\u001a\u001a\u00020\u00052\u0006\u00104\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u00105J\u0018\u00106\u001a\u0004\u0018\u00010\u00152\u0006\u00107\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001e\u00108\u001a\b\u0012\u0004\u0012\u0002090\u000f2\b\b\u0002\u0010:\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u001c\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00130\u000f2\u0006\u0010\u001c\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001e\u0010<\u001a\b\u0012\u0004\u0012\u00020\u001e0\u000f2\b\b\u0002\u0010:\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u0018\u0010=\u001a\u0004\u0018\u00010\u00172\u0006\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001e\u0010>\u001a\b\u0012\u0004\u0012\u00020?0\u000f2\b\b\u0002\u0010:\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u001e\u0010@\u001a\b\u0012\u0004\u0012\u00020!0\u000f2\b\b\u0002\u0010:\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u001e\u0010A\u001a\b\u0012\u0004\u0012\u00020.0\u000f2\b\b\u0002\u0010:\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u0018\u0010B\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u001a\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010C\u001a\b\u0012\u0004\u0012\u00020&0\u000f2\b\b\u0002\u0010:\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u00100J\u001a\u0010D\u001a\u0004\u0018\u00010E2\b\b\u0002\u0010F\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010G\u001a\u00020\u00052\u0006\u0010H\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010IJ\u0016\u0010J\u001a\u00020\u00052\u0006\u0010K\u001a\u00020?H\u00a7@\u00a2\u0006\u0002\u0010LJ\u0016\u0010M\u001a\u00020\u00052\u0006\u0010N\u001a\u00020!H\u00a7@\u00a2\u0006\u0002\u0010OJ\u0016\u0010P\u001a\u00020\u00052\u0006\u0010Q\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010RJ\u0016\u0010S\u001a\u00020\u00052\u0006\u0010T\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010UJ\u0016\u0010V\u001a\u00020\u00052\u0006\u0010H\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010WJ\u0016\u0010X\u001a\u00020\u00052\u0006\u0010Y\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010ZJ\u0016\u0010[\u001a\u00020\u00052\u0006\u0010\\\u001a\u00020\u001eH\u00a7@\u00a2\u0006\u0002\u0010]J\u0016\u0010^\u001a\u00020\u00052\u0006\u0010_\u001a\u00020&H\u00a7@\u00a2\u0006\u0002\u0010`J\u0016\u0010a\u001a\u00020\u00052\u0006\u0010b\u001a\u00020EH\u00a7@\u00a2\u0006\u0002\u0010cJ\u0016\u0010d\u001a\u00020\u00032\u0006\u0010H\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010WJ \u0010e\u001a\u00020\u00032\u0006\u0010f\u001a\u00020\u00052\b\b\u0002\u0010g\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u00105J\u0016\u0010h\u001a\u00020\u00032\u0006\u0010b\u001a\u00020EH\u00a7@\u00a2\u0006\u0002\u0010c\u00a8\u0006i"}, d2 = {"Lcom/zara/assistant/data/local/dao/UserLearningDao;", "", "deactivatePattern", "", "patternId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldInteractions", "", "cutoffTime", "deletePreference", "key", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveBehavioralPatterns", "", "Lcom/zara/assistant/domain/model/BehavioralPattern;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActivePatterns", "Lcom/zara/assistant/domain/model/UserPattern;", "getAllActiveModels", "Lcom/zara/assistant/domain/model/MLModelData;", "getAllPreferences", "Lcom/zara/assistant/domain/model/UserPreference;", "getAverageExecutionTime", "", "startTime", "getBehavioralPatternsByType", "type", "getCachedSearch", "Lcom/zara/assistant/domain/model/SearchCache;", "query", "getConversationsBySession", "Lcom/zara/assistant/domain/model/ConversationHistory;", "sessionId", "getConversationsByTopic", "topic", "getFavoritesByCategory", "Lcom/zara/assistant/domain/model/UserFavorite;", "category", "getHighConfidencePatterns", "minConfidence", "", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInteractionCountSince", "getInteractionsByDayOfWeek", "Lcom/zara/assistant/domain/model/UserInteraction;", "dayOfWeek", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInteractionsByHour", "hour", "getInteractionsByTimeRange", "endTime", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMLModelData", "modelName", "getMostFrequentCommands", "Lcom/zara/assistant/data/local/dao/CommandFrequency;", "limit", "getPatternsByType", "getPopularSearches", "getPreference", "getRecentContextualData", "Lcom/zara/assistant/domain/model/ContextualData;", "getRecentConversations", "getRecentInteractions", "getSuccessRate", "getTopFavorites", "getUserProfile", "Lcom/zara/assistant/domain/model/UserProfile;", "userId", "insertBehavioralPattern", "pattern", "(Lcom/zara/assistant/domain/model/BehavioralPattern;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertContextualData", "context", "(Lcom/zara/assistant/domain/model/ContextualData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertConversationHistory", "conversation", "(Lcom/zara/assistant/domain/model/ConversationHistory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertInteraction", "interaction", "(Lcom/zara/assistant/domain/model/UserInteraction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertMLModelData", "modelData", "(Lcom/zara/assistant/domain/model/MLModelData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertPattern", "(Lcom/zara/assistant/domain/model/UserPattern;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertPreference", "preference", "(Lcom/zara/assistant/domain/model/UserPreference;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertSearchCache", "cache", "(Lcom/zara/assistant/domain/model/SearchCache;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertUserFavorite", "favorite", "(Lcom/zara/assistant/domain/model/UserFavorite;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertUserProfile", "profile", "(Lcom/zara/assistant/domain/model/UserProfile;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePattern", "updateSearchAccess", "id", "timestamp", "updateUserProfile", "app_debug"})
@androidx.room.Dao()
public abstract interface UserLearningDao {
    
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertInteraction(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserInteraction interaction, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_interactions ORDER BY timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentInteractions(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserInteraction>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_interactions WHERE timestamp >= :startTime AND timestamp <= :endTime")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInteractionsByTimeRange(long startTime, long endTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserInteraction>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_interactions WHERE timeOfDay = :hour")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInteractionsByHour(int hour, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserInteraction>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_interactions WHERE dayOfWeek = :dayOfWeek")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInteractionsByDayOfWeek(int dayOfWeek, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserInteraction>> $completion);
    
    @androidx.room.Query(value = "SELECT command, COUNT(*) as frequency FROM user_interactions WHERE success = 1 GROUP BY command ORDER BY frequency DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMostFrequentCommands(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.dao.CommandFrequency>> $completion);
    
    @androidx.room.Query(value = "DELETE FROM user_interactions WHERE timestamp < :cutoffTime")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldInteractions(long cutoffTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPattern(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserPattern pattern, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePattern(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserPattern pattern, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_patterns WHERE isActive = 1 ORDER BY confidence DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getActivePatterns(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserPattern>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_patterns WHERE patternType = :type AND isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPatternsByType(@org.jetbrains.annotations.NotNull()
    java.lang.String type, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserPattern>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_patterns WHERE confidence >= :minConfidence AND isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getHighConfidencePatterns(float minConfidence, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserPattern>> $completion);
    
    @androidx.room.Query(value = "UPDATE user_patterns SET isActive = 0 WHERE id = :patternId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deactivatePattern(long patternId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPreference(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserPreference preference, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_preferences WHERE preferenceKey = :key")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.UserPreference> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_preferences ORDER BY confidence DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserPreference>> $completion);
    
    @androidx.room.Query(value = "DELETE FROM user_preferences WHERE preferenceKey = :key")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePreference(@org.jetbrains.annotations.NotNull()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertUserProfile(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserProfile profile, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_profile WHERE id = :userId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserProfile(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.UserProfile> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUserProfile(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserProfile profile, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertConversationHistory(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ConversationHistory conversation, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM conversation_history ORDER BY timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentConversations(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationHistory>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM conversation_history WHERE sessionId = :sessionId ORDER BY timestamp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationsBySession(@org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationHistory>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM conversation_history WHERE topics LIKE \'%\' || :topic || \'%\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationsByTopic(@org.jetbrains.annotations.NotNull()
    java.lang.String topic, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationHistory>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSearchCache(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.SearchCache cache, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM search_cache WHERE query = :query ORDER BY timestamp DESC LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCachedSearch(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.SearchCache> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM search_cache ORDER BY accessCount DESC, timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPopularSearches(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.SearchCache>> $completion);
    
    @androidx.room.Query(value = "UPDATE search_cache SET accessCount = accessCount + 1, lastAccessed = :timestamp WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSearchAccess(long id, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertUserFavorite(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.UserFavorite favorite, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_favorites WHERE category = :category ORDER BY confidence DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFavoritesByCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserFavorite>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM user_favorites ORDER BY confidence DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTopFavorites(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.UserFavorite>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertBehavioralPattern(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.BehavioralPattern pattern, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM behavioral_patterns WHERE isActive = 1 ORDER BY confidence DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getActiveBehavioralPatterns(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.BehavioralPattern>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM behavioral_patterns WHERE patternType = :type AND isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBehavioralPatternsByType(@org.jetbrains.annotations.NotNull()
    java.lang.String type, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.BehavioralPattern>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertMLModelData(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.MLModelData modelData, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM ml_model_data WHERE modelName = :modelName AND isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMLModelData(@org.jetbrains.annotations.NotNull()
    java.lang.String modelName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.MLModelData> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM ml_model_data WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllActiveModels(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.MLModelData>> $completion);
    
    @androidx.room.Insert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertContextualData(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.ContextualData context, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM contextual_data ORDER BY timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentContextualData(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ContextualData>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM user_interactions WHERE timestamp >= :startTime")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInteractionCountSince(long startTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(executionTimeMs) FROM user_interactions WHERE success = 1 AND timestamp >= :startTime")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageExecutionTime(long startTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT (COUNT(CASE WHEN success = 1 THEN 1 END) * 100.0 / COUNT(*)) as successRate FROM user_interactions WHERE timestamp >= :startTime")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSuccessRate(long startTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    /**
     * Data Access Object for user learning data
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}