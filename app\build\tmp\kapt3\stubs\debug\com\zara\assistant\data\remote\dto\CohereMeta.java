package com.zara.assistant.data.remote.dto;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0019\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\u000b\u0010\u000b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J!\u0010\r\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0015"}, d2 = {"Lcom/zara/assistant/data/remote/dto/CohereMeta;", "", "api_version", "Lcom/zara/assistant/data/remote/dto/CohereApiVersion;", "billed_units", "Lcom/zara/assistant/data/remote/dto/CohereBilledUnits;", "(Lcom/zara/assistant/data/remote/dto/CohereApiVersion;Lcom/zara/assistant/data/remote/dto/CohereBilledUnits;)V", "getApi_version", "()Lcom/zara/assistant/data/remote/dto/CohereApiVersion;", "getBilled_units", "()Lcom/zara/assistant/data/remote/dto/CohereBilledUnits;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class CohereMeta {
    @org.jetbrains.annotations.Nullable()
    private final com.zara.assistant.data.remote.dto.CohereApiVersion api_version = null;
    @org.jetbrains.annotations.Nullable()
    private final com.zara.assistant.data.remote.dto.CohereBilledUnits billed_units = null;
    
    public CohereMeta(@org.jetbrains.annotations.Nullable()
    com.zara.assistant.data.remote.dto.CohereApiVersion api_version, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.data.remote.dto.CohereBilledUnits billed_units) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.data.remote.dto.CohereApiVersion getApi_version() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.data.remote.dto.CohereBilledUnits getBilled_units() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.data.remote.dto.CohereApiVersion component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.zara.assistant.data.remote.dto.CohereBilledUnits component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.data.remote.dto.CohereMeta copy(@org.jetbrains.annotations.Nullable()
    com.zara.assistant.data.remote.dto.CohereApiVersion api_version, @org.jetbrains.annotations.Nullable()
    com.zara.assistant.data.remote.dto.CohereBilledUnits billed_units) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}