// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import android.speech.tts.TextToSpeech;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_Companion_ProvideTextToSpeechFactory implements Factory<TextToSpeech> {
  private final Provider<Context> contextProvider;

  public AppModule_Companion_ProvideTextToSpeechFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public TextToSpeech get() {
    return provideTextToSpeech(contextProvider.get());
  }

  public static AppModule_Companion_ProvideTextToSpeechFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_Companion_ProvideTextToSpeechFactory(contextProvider);
  }

  public static TextToSpeech provideTextToSpeech(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.Companion.provideTextToSpeech(context));
  }
}
