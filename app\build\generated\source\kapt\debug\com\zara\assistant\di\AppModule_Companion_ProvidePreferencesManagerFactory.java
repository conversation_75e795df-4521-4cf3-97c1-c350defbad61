// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import com.zara.assistant.data.local.preferences.PreferencesManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_Companion_ProvidePreferencesManagerFactory implements Factory<PreferencesManager> {
  private final Provider<DataStore<Preferences>> dataStoreProvider;

  public AppModule_Companion_ProvidePreferencesManagerFactory(
      Provider<DataStore<Preferences>> dataStoreProvider) {
    this.dataStoreProvider = dataStoreProvider;
  }

  @Override
  public PreferencesManager get() {
    return providePreferencesManager(dataStoreProvider.get());
  }

  public static AppModule_Companion_ProvidePreferencesManagerFactory create(
      Provider<DataStore<Preferences>> dataStoreProvider) {
    return new AppModule_Companion_ProvidePreferencesManagerFactory(dataStoreProvider);
  }

  public static PreferencesManager providePreferencesManager(DataStore<Preferences> dataStore) {
    return Preconditions.checkNotNullFromProvides(AppModule.Companion.providePreferencesManager(dataStore));
  }
}
