package com.zara.assistant.data.repository;

/**
 * Implementation of AIRepository
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0003\n\u0002\b\u001a\b\u0007\u0018\u0000 `2\u00020\u0001:\u0001`B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J4\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\f0\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0096@\u00a2\u0006\u0002\u0010\u0016J\u001e\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\rH\u0096@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001dH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001f\u0010 J\u001c\u0010!\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001dH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010 J$\u0010#\u001a\b\u0012\u0004\u0012\u00020$0\u001d2\u0006\u0010%\u001a\u00020\fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b&\u0010\'J\u000e\u0010(\u001a\u00020\u0018H\u0096@\u00a2\u0006\u0002\u0010 J\u0010\u0010)\u001a\u00020\u001e2\u0006\u0010%\u001a\u00020\fH\u0002J\u0010\u0010*\u001a\u00020\u001e2\u0006\u0010%\u001a\u00020\fH\u0002J\u0010\u0010+\u001a\u00020\u001e2\u0006\u0010%\u001a\u00020\fH\u0002J\u0010\u0010,\u001a\u00020\u001e2\u0006\u0010%\u001a\u00020\fH\u0002J\u0010\u0010-\u001a\u00020\f2\u0006\u0010%\u001a\u00020\fH\u0002J\u001c\u0010.\u001a\b\u0012\u0004\u0012\u00020/0\u00112\u0006\u0010\u001a\u001a\u00020\rH\u0096@\u00a2\u0006\u0002\u00100J\u0016\u0010.\u001a\b\u0012\u0004\u0012\u00020/0\u00112\u0006\u00101\u001a\u00020\fH\u0002J \u00102\u001a\u00020\f2\u0006\u0010%\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u00103\u001a\u00020\u0015H\u0002J4\u00104\u001a\b\u0012\u0004\u0012\u00020\f0\u001d2\u0006\u00105\u001a\u00020\f2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u000209H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010;JB\u0010<\u001a\b\u0012\u0004\u0012\u00020\r0\u001d2\u0006\u0010\u000f\u001a\u00020=2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\f0\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b>\u0010?J\u0018\u0010@\u001a\u0004\u0018\u00010\r2\u0006\u0010\u0019\u001a\u00020\fH\u0096@\u00a2\u0006\u0002\u0010\'J\u0016\u0010A\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020=H\u0096@\u00a2\u0006\u0002\u0010BJ\u000e\u0010C\u001a\b\u0012\u0004\u0012\u00020\t0DH\u0016J\u001e\u0010E\u001a\u00020\r2\u0006\u0010F\u001a\u00020G2\u0006\u0010\u000f\u001a\u00020=H\u0096@\u00a2\u0006\u0002\u0010HJ$\u0010I\u001a\b\u0012\u0004\u0012\u00020\r0\u001d2\u0006\u0010\u000f\u001a\u00020=H\u0082@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bJ\u0010BJ$\u0010K\u001a\b\u0012\u0004\u0012\u00020\r0\u001d2\u0006\u0010\u000f\u001a\u00020=H\u0082@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bL\u0010BJ$\u0010M\u001a\b\u0012\u0004\u0012\u00020\r0\u001d2\u0006\u0010\u000f\u001a\u00020=H\u0082@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bN\u0010BJ$\u0010O\u001a\b\u0012\u0004\u0012\u00020\r0\u001d2\u0006\u0010\u000f\u001a\u00020=H\u0082@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bP\u0010BJ$\u0010Q\u001a\b\u0012\u0004\u0012\u00020\r0\u001d2\u0006\u0010\u000f\u001a\u00020=H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bR\u0010BJ\u0018\u0010S\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\f2\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J,\u0010T\u001a\b\u0012\u0004\u0012\u00020\f0\u001d2\u0006\u0010U\u001a\u00020\f2\u0006\u00106\u001a\u000207H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bV\u0010WJ\u0016\u0010X\u001a\u00020\u00182\u0006\u0010Y\u001a\u00020\tH\u0096@\u00a2\u0006\u0002\u0010ZJ\u0016\u0010[\u001a\b\u0012\u0004\u0012\u00020\f0D2\u0006\u0010\u000f\u001a\u00020=H\u0016J*\u0010\\\u001a\b\u0012\u0004\u0012\u00020\f0\u001d2\f\u0010]\u001a\b\u0012\u0004\u0012\u00020\f0\u0011H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b^\u0010_R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\r0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006a"}, d2 = {"Lcom/zara/assistant/data/repository/AIRepositoryImpl;", "Lcom/zara/assistant/domain/repository/AIRepository;", "cohereApiService", "Lcom/zara/assistant/data/remote/api/CohereApiService;", "perplexityApiService", "Lcom/zara/assistant/data/remote/api/PerplexityApiService;", "(Lcom/zara/assistant/data/remote/api/CohereApiService;Lcom/zara/assistant/data/remote/api/PerplexityApiService;)V", "_preferredAISource", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/zara/assistant/domain/model/AISource;", "responseCache", "", "", "Lcom/zara/assistant/domain/model/AIResponse;", "buildContextPrompt", "command", "conversationHistory", "", "personality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "responseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "(Ljava/lang/String;Ljava/util/List;Lcom/zara/assistant/core/Constants$AIPersonality;Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheResponse", "", "commandHash", "response", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/AIResponse;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkCohereHealth", "Lkotlin/Result;", "", "checkCohereHealth-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkPerplexityHealth", "checkPerplexityHealth-IoAF18A", "classifyCommand", "Lcom/zara/assistant/domain/model/CommandType;", "text", "classifyCommand-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearResponseCache", "containsCommunicationKeywords", "containsEntertainmentKeywords", "containsInformationKeywords", "containsSystemKeywords", "extractAppName", "extractSystemActions", "Lcom/zara/assistant/domain/model/SystemAction;", "(Lcom/zara/assistant/domain/model/AIResponse;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "responseText", "generateCacheKey", "style", "generateCohereResponse", "prompt", "maxTokens", "", "temperature", "", "generateCohereResponse-BWLJW6A", "(Ljava/lang/String;IFLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateResponse", "Lcom/zara/assistant/domain/model/VoiceCommand;", "generateResponse-yxL6bBk", "(Lcom/zara/assistant/domain/model/VoiceCommand;Ljava/util/List;Lcom/zara/assistant/core/Constants$AIPersonality;Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCachedResponse", "getFallbackResponse", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPreferredAISource", "Lkotlinx/coroutines/flow/Flow;", "handleAPIError", "error", "", "(Ljava/lang/Throwable;Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleCommunicationCommand", "handleCommunicationCommand-gIAlu-s", "handleEntertainmentCommand", "handleEntertainmentCommand-gIAlu-s", "handleInformationQuery", "handleInformationQuery-gIAlu-s", "handleSystemCommand", "handleSystemCommand-gIAlu-s", "processCommand", "processCommand-gIAlu-s", "processResponseForVoice", "searchPerplexity", "query", "searchPerplexity-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setPreferredAISource", "source", "(Lcom/zara/assistant/domain/model/AISource;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "streamResponse", "summarizeConversation", "messages", "summarizeConversation-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class AIRepositoryImpl implements com.zara.assistant.domain.repository.AIRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.data.remote.api.CohereApiService cohereApiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.data.remote.api.PerplexityApiService perplexityApiService = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AIRepositoryImpl";
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.zara.assistant.domain.model.AIResponse> responseCache = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.zara.assistant.domain.model.AISource> _preferredAISource = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.data.repository.AIRepositoryImpl.Companion Companion = null;
    
    @javax.inject.Inject()
    public AIRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.api.CohereApiService cohereApiService, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.remote.api.PerplexityApiService perplexityApiService) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object extractSystemActions(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.AIResponse response, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.SystemAction>> $completion) {
        return null;
    }
    
    private final java.util.List<com.zara.assistant.domain.model.SystemAction> extractSystemActions(java.lang.String responseText) {
        return null;
    }
    
    /**
     * Post-process AI response to make it more suitable for voice output
     */
    private final java.lang.String processResponseForVoice(java.lang.String response, com.zara.assistant.core.Constants.AIResponseStyle responseStyle) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object buildContextPrompt(@org.jetbrains.annotations.NotNull()
    java.lang.String command, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> conversationHistory, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIPersonality personality, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIResponseStyle responseStyle, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getCachedResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String commandHash, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object cacheResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String commandHash, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.AIResponse response, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object clearResponseCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.AISource> getPreferredAISource() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setPreferredAISource(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.AISource source, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.String> streamResponse(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceCommand command) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getFallbackResponse(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceCommand command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object handleAPIError(@org.jetbrains.annotations.NotNull()
    java.lang.Throwable error, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceCommand command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AIResponse> $completion) {
        return null;
    }
    
    private final java.lang.String generateCacheKey(java.lang.String text, com.zara.assistant.core.Constants.AIPersonality personality, com.zara.assistant.core.Constants.AIResponseStyle style) {
        return null;
    }
    
    private final boolean containsSystemKeywords(java.lang.String text) {
        return false;
    }
    
    private final boolean containsInformationKeywords(java.lang.String text) {
        return false;
    }
    
    private final boolean containsCommunicationKeywords(java.lang.String text) {
        return false;
    }
    
    private final boolean containsEntertainmentKeywords(java.lang.String text) {
        return false;
    }
    
    private final java.lang.String extractAppName(java.lang.String text) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/data/repository/AIRepositoryImpl$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}