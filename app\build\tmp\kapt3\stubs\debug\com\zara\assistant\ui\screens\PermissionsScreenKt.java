package com.zara.assistant.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a0\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001a.\u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\r"}, d2 = {"PermissionItem", "", "permissionInfo", "Lcom/zara/assistant/ui/screens/PermissionInfo;", "isGranted", "", "onRequestPermission", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "PermissionsScreen", "onPermissionsGranted", "onNavigateBack", "app_debug"})
public final class PermissionsScreenKt {
    
    /**
     * Screen for requesting and managing permissions
     */
    @androidx.compose.runtime.Composable()
    public static final void PermissionsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionsGranted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PermissionItem(com.zara.assistant.ui.screens.PermissionInfo permissionInfo, boolean isGranted, kotlin.jvm.functions.Function0<kotlin.Unit> onRequestPermission, androidx.compose.ui.Modifier modifier) {
    }
}