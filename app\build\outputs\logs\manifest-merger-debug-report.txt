-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:221:9-230:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:225:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:223:13-68
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:224:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:222:13-67
manifest
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-234:12
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-234:12
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-234:12
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:1-234:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c12b3b2ba3a27eedd02733c31aef3b3\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\47494bae0d5af340ce384dde3add152e\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2e7ce60078dca7681ade94b449a3f24\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\59359da0c1ee57da45e4230e6821c80b\transformed\jetified-hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\91ef250a5d5b5118576e24e915eeb879\transformed\jetified-hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e0bfe12f7141ddb20b620d438473b674\transformed\navigation-common-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\47037f0421d3d9b11d13fcaf80c6e56d\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\9996ac6e67d4e7277455466146b70625\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\4fe9c0b5c2a76bfb8e1b79ac7121b75c\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\7fef4eac5ca1a4e38a9faae6ebdf41d9\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a59f71f8e3679e87585966f0c1cd202\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a90cbfff0bb8a30188f12d9d3faeeb7f\transformed\jetified-hilt-work-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\44bcc13602f1f3f77422f4678622a4e2\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac2f50916fa650919f030349484e55e3\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\687b518b080c7a1393d00f0b0e33bae4\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a245b8c555f9375bc255d6a8736f2b24\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c30a1884470f5b11c60f5215e79d996\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7f544a67be42467f6f17e65a35be5d7\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0a26ae515b0c562da57d41a9b768e19c\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\44c6728ebaab3a0d94179b22f311ef2b\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5acbc661a2593b21a8dc705fd331013\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1876129b9e490c2861a5ecf91d698967\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b7e9970f43b9edcd985c2ed1b6ff7df\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a068144363efea7525570fa4d00f4723\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\83421d97c751ab086004a15f31653fc1\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ff33d4e7e8473bae8f70c1b7a6bd969\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bb5778a78185296455c421aac6c94e13\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7af755cab61e90f344fd2e601306ba48\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bdde54c0362d49e9297f3f6cdba91e0\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\9790cb22a3b463dd9fcd4d08895892df\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f6da3a8b03a6e9fbcc293ab92e3aea8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ae8d81f1a339add5d2c6e266cd41ee9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c74c7aa0104acf8f676d8be4828c980b\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7c1848ce161fad5bf8d6ea7b6ea0e3b\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7df619def1add6631ff259a137c4bb5\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b19195aca2886c6d8a39e940da0e42a3\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\453917bc7e0eacb6ea017ec9ae7415d6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce76e19c0f577893b121ce2d25432a6\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\60353292701091173a0ecf6b3ab86996\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f335a0707ca7519e8d599c8a3c4637e5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\79a24acaa9238f46f225dfd6663a45f0\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\71be49c77ca0cc81d859e2cd8dce612e\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d9a5df90f2c043e6b07b53cb3d59fe2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\72ffab65b474ff1a01d0c942aec3b8e4\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\31ab22f75a7b24d457c34fde6a28b4f5\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e225aee254d12ca850d1464a9b1ad53\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ecb7be72ebd258ebb899bf8355eead54\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\8769769ca03bb1c6402f57b30feb2baf\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\f6aff994c0928c6bc09876e0d32c2d50\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\caa642afe9201c8bf8dfdbad69a7ee4b\transformed\jetified-activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [ai.picovoice:porcupine-android:3.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\25db9e9980e559d97eeb32074fb8bd77\transformed\jetified-porcupine-android-3.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [ai.picovoice:android-voice-processor:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\738a969318ba67308a2d8c269c4236c6\transformed\jetified-android-voice-processor-1.0.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f74cc867abd30b8688c7ae6529065ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\22558a89f6d65b3648d7dad30bc91e1e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5374e1cacf5ffd11fca12e0695f3864d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a6484b74439302b8a4570a2f24011ed\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\215b388b83b16a90f62348e2bcbde19b\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec3db78c55efdeced6819ea166fdaa03\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f16a10750b970ea1fc01166532608aa1\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ca0619ccb7ea7f59335631e58e8df8d\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78c6b648ed446034e57417531717e962\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\b58c2cad4f3d07c5d5d69dcfa1d96289\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cb3a926481a9678d3fb79c6ab1c379a\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f806a1774dbedb49d30e5f8663b8785\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c03cb0101fcf02cfeba542bac3104662\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\771c6f9737c7d57ce2e3bdd503381a3e\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\63938fb9d8f0ce47aeb6e0fa6294e058\transformed\jetified-napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\284bdafae067a0f3a90fa6dc08e9dcd1\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b57f9381ff59d7d765a2f23afc676697\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\982d830af71125e23d1d43478738237a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb532ef1d629465f04f84e4d1d93bd30\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba235b4531af3319253ff2fa28da5fd7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\501622f65ed3684b84b48b1ba22e54b2\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\414541ca25323672fae07835fcdb26bf\transformed\jetified-tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\29bb4f23fe0ee5ed4b52ef8e80afeb4b\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-task-text:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\47441b91c3afecee0d60009c069bdd38\transformed\jetified-tensorflow-lite-task-text-0.4.4\AndroidManifest.xml:2:1-9:12
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:2:1-21:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\0c6f5492bb3e44ff32a78c82023c09be\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\6d85e2d9d46aba4b83881e1244955e01\transformed\jetified-tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee0533333a4694945cdab2604154b76\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-task-base:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\208b13092f829d2408d56a611a023089\transformed\jetified-tensorflow-lite-task-base-0.4.4\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\70b3979cf6b38d608dc83128a5aa880d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:5-71
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:11:5-71
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:5-67
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:12:5-67
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:12:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:22-76
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:22-75
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:17:5-88
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:17:22-85
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:21:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:21:22-73
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:28:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:28:22-75
uses-permission#android.permission.BLUETOOTH_PRIVILEGED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:29:5-30:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:30:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:29:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:22-76
uses-permission#android.permission.CONNECTIVITY_INTERNAL
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:5-35:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:35:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:22-77
uses-permission#android.permission.TETHER_PRIVILEGED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:38:5-39:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:39:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:38:22-73
uses-permission#android.permission.NFC
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:5-62
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:22-59
uses-permission#android.permission.NFC_PREFERRED_PAYMENT_INFO
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:22-82
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:22-82
uses-permission#android.permission.EXPAND_STATUS_BAR
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:22-73
uses-permission#android.permission.REORDER_TASKS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:52:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:52:22-69
uses-permission#android.permission.GET_TASKS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:53:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:53:22-65
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:5-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:22-81
uses-permission#android.permission.SET_WALLPAPER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:22-69
uses-permission#android.permission.SET_WALLPAPER_HINTS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:56:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:56:22-75
uses-permission#android.permission.DEVICE_POWER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:5-58:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:58:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:22-68
uses-permission#android.permission.REBOOT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:5-60:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:60:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:22-62
uses-permission#android.permission.CHANGE_CONFIGURATION
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:22-76
uses-permission#android.permission.FLASHLIGHT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:22-66
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:22-72
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:22-74
uses-permission#android.permission.MODIFY_PHONE_STATE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:5-70:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:70:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:22-74
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:73:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:73:22-74
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-75:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:75:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:22-75
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:22-70
uses-permission#android.permission.WRITE_SECURE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:5-80:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:80:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:22-77
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:22-82
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:5-93
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:22-90
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:22-74
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:88:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:88:22-82
uses-permission#android.permission.CALL_PHONE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:22-66
uses-permission#android.permission.SEND_SMS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:22-64
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:22-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:22-78
uses-permission#android.permission.INTERACT_ACROSS_USERS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:5-101:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:101:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:22-77
uses-permission#android.permission.MANAGE_USERS
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:102:5-103:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:103:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:102:22-68
uses-permission#android.permission.STATUS_BAR
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:104:5-105:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:105:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:104:22-66
uses-permission#android.permission.ACCESS_SUPERUSER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:5-109:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:109:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:22-72
uses-permission#android.permission.HARDWARE_TEST
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:5-111:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:111:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:22-69
uses-permission#android.permission.BIND_DEVICE_ADMIN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:22-73
application
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:5-232:19
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:5-232:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b57f9381ff59d7d765a2f23afc676697\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b57f9381ff59d7d765a2f23afc676697\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\29bb4f23fe0ee5ed4b52ef8e80afeb4b\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\29bb4f23fe0ee5ed4b52ef8e80afeb4b\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:14:5-19:19
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:14:5-19:19
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee0533333a4694945cdab2604154b76\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee0533333a4694945cdab2604154b76\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-task-base:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\208b13092f829d2408d56a611a023089\transformed\jetified-tensorflow-lite-task-base-0.4.4\AndroidManifest.xml:4:4-5:18
MERGED from [org.tensorflow:tensorflow-lite-task-base:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\208b13092f829d2408d56a611a023089\transformed\jetified-tensorflow-lite-task-base-0.4.4\AndroidManifest.xml:4:4-5:18
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\70b3979cf6b38d608dc83128a5aa880d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\70b3979cf6b38d608dc83128a5aa880d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:128:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:126:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:124:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:127:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:131:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:122:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:129:9-42
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:123:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:130:9-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:121:9-40
activity#com.zara.assistant.ui.MainActivity
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:134:9-148:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:139:13-43
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:136:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:13-46
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:135:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:140:13-143:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:141:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:141:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:142:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:142:27-74
intent-filter#action:name:android.intent.action.VOICE_COMMAND+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:144:13-147:29
action#android.intent.action.VOICE_COMMAND
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:25-75
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:27-73
activity#com.zara.assistant.ui.RestrictedSettingsGuideActivity
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:9-155:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:155:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:153:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:154:13-46
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:152:13-63
service#com.zara.assistant.services.WakeWordService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:158:9-162:58
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:160:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:161:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:162:13-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:159:13-53
service#com.zara.assistant.services.AdvancedVoiceProcessingService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:165:9-169:58
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:167:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:168:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:169:13-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:166:13-68
service#com.zara.assistant.services.ZaraAccessibilityService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:172:9-182:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:175:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:174:13-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:173:13-62
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:176:13-178:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:177:17-92
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:177:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:179:13-181:72
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:181:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:180:17-60
receiver#com.zara.assistant.services.ZaraDeviceAdminReceiver
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:185:9-196:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:188:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:187:13-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:186:13-61
meta-data#android.app.device_admin
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:189:13-191:63
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:191:17-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:190:17-56
intent-filter#action:name:android.app.action.DEVICE_ADMIN_DISABLED+action:name:android.app.action.DEVICE_ADMIN_ENABLED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:192:13-195:29
action#android.app.action.DEVICE_ADMIN_ENABLED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:193:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:193:25-79
action#android.app.action.DEVICE_ADMIN_DISABLED
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:194:17-83
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:194:25-80
service#com.zara.assistant.services.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:199:9-206:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:202:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:201:13-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:200:13-65
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:203:13-205:29
action#android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:204:17-99
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:204:25-96
service#com.zara.assistant.services.AIProcessingService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:209:9-212:40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:211:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:212:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:210:13-57
service#com.zara.assistant.services.UserLearningService
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:215:9-218:40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:217:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:218:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:216:13-57
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:226:13-229:39
REJECTED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:229:17-36
	android:value
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:228:17-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:227:17-68
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c12b3b2ba3a27eedd02733c31aef3b3\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c12b3b2ba3a27eedd02733c31aef3b3\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\47494bae0d5af340ce384dde3add152e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\47494bae0d5af340ce384dde3add152e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2e7ce60078dca7681ade94b449a3f24\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2e7ce60078dca7681ade94b449a3f24\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\59359da0c1ee57da45e4230e6821c80b\transformed\jetified-hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\59359da0c1ee57da45e4230e6821c80b\transformed\jetified-hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\91ef250a5d5b5118576e24e915eeb879\transformed\jetified-hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\91ef250a5d5b5118576e24e915eeb879\transformed\jetified-hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e0bfe12f7141ddb20b620d438473b674\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e0bfe12f7141ddb20b620d438473b674\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\47037f0421d3d9b11d13fcaf80c6e56d\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\47037f0421d3d9b11d13fcaf80c6e56d\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\9996ac6e67d4e7277455466146b70625\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\9996ac6e67d4e7277455466146b70625\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\4fe9c0b5c2a76bfb8e1b79ac7121b75c\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\4fe9c0b5c2a76bfb8e1b79ac7121b75c\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\7fef4eac5ca1a4e38a9faae6ebdf41d9\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\7fef4eac5ca1a4e38a9faae6ebdf41d9\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a59f71f8e3679e87585966f0c1cd202\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a59f71f8e3679e87585966f0c1cd202\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a90cbfff0bb8a30188f12d9d3faeeb7f\transformed\jetified-hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a90cbfff0bb8a30188f12d9d3faeeb7f\transformed\jetified-hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\44bcc13602f1f3f77422f4678622a4e2\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\44bcc13602f1f3f77422f4678622a4e2\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac2f50916fa650919f030349484e55e3\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac2f50916fa650919f030349484e55e3\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\687b518b080c7a1393d00f0b0e33bae4\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\687b518b080c7a1393d00f0b0e33bae4\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a245b8c555f9375bc255d6a8736f2b24\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a245b8c555f9375bc255d6a8736f2b24\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c30a1884470f5b11c60f5215e79d996\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c30a1884470f5b11c60f5215e79d996\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7f544a67be42467f6f17e65a35be5d7\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7f544a67be42467f6f17e65a35be5d7\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0a26ae515b0c562da57d41a9b768e19c\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0a26ae515b0c562da57d41a9b768e19c\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\44c6728ebaab3a0d94179b22f311ef2b\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\44c6728ebaab3a0d94179b22f311ef2b\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5acbc661a2593b21a8dc705fd331013\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5acbc661a2593b21a8dc705fd331013\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1876129b9e490c2861a5ecf91d698967\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1876129b9e490c2861a5ecf91d698967\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b7e9970f43b9edcd985c2ed1b6ff7df\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b7e9970f43b9edcd985c2ed1b6ff7df\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a068144363efea7525570fa4d00f4723\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a068144363efea7525570fa4d00f4723\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\83421d97c751ab086004a15f31653fc1\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\83421d97c751ab086004a15f31653fc1\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ff33d4e7e8473bae8f70c1b7a6bd969\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ff33d4e7e8473bae8f70c1b7a6bd969\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bb5778a78185296455c421aac6c94e13\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bb5778a78185296455c421aac6c94e13\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7af755cab61e90f344fd2e601306ba48\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7af755cab61e90f344fd2e601306ba48\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bdde54c0362d49e9297f3f6cdba91e0\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bdde54c0362d49e9297f3f6cdba91e0\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\9790cb22a3b463dd9fcd4d08895892df\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\9790cb22a3b463dd9fcd4d08895892df\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f6da3a8b03a6e9fbcc293ab92e3aea8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f6da3a8b03a6e9fbcc293ab92e3aea8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ae8d81f1a339add5d2c6e266cd41ee9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2ae8d81f1a339add5d2c6e266cd41ee9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c74c7aa0104acf8f676d8be4828c980b\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c74c7aa0104acf8f676d8be4828c980b\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7c1848ce161fad5bf8d6ea7b6ea0e3b\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7c1848ce161fad5bf8d6ea7b6ea0e3b\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7df619def1add6631ff259a137c4bb5\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7df619def1add6631ff259a137c4bb5\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b19195aca2886c6d8a39e940da0e42a3\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b19195aca2886c6d8a39e940da0e42a3\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\453917bc7e0eacb6ea017ec9ae7415d6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\453917bc7e0eacb6ea017ec9ae7415d6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce76e19c0f577893b121ce2d25432a6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce76e19c0f577893b121ce2d25432a6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\60353292701091173a0ecf6b3ab86996\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\60353292701091173a0ecf6b3ab86996\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f335a0707ca7519e8d599c8a3c4637e5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f335a0707ca7519e8d599c8a3c4637e5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\79a24acaa9238f46f225dfd6663a45f0\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\79a24acaa9238f46f225dfd6663a45f0\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\71be49c77ca0cc81d859e2cd8dce612e\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\71be49c77ca0cc81d859e2cd8dce612e\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d9a5df90f2c043e6b07b53cb3d59fe2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d9a5df90f2c043e6b07b53cb3d59fe2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\72ffab65b474ff1a01d0c942aec3b8e4\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\72ffab65b474ff1a01d0c942aec3b8e4\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\31ab22f75a7b24d457c34fde6a28b4f5\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\31ab22f75a7b24d457c34fde6a28b4f5\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e225aee254d12ca850d1464a9b1ad53\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e225aee254d12ca850d1464a9b1ad53\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ecb7be72ebd258ebb899bf8355eead54\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ecb7be72ebd258ebb899bf8355eead54\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\8769769ca03bb1c6402f57b30feb2baf\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\8769769ca03bb1c6402f57b30feb2baf\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\f6aff994c0928c6bc09876e0d32c2d50\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\f6aff994c0928c6bc09876e0d32c2d50\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\caa642afe9201c8bf8dfdbad69a7ee4b\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\caa642afe9201c8bf8dfdbad69a7ee4b\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [ai.picovoice:porcupine-android:3.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\25db9e9980e559d97eeb32074fb8bd77\transformed\jetified-porcupine-android-3.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [ai.picovoice:porcupine-android:3.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\25db9e9980e559d97eeb32074fb8bd77\transformed\jetified-porcupine-android-3.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [ai.picovoice:android-voice-processor:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\738a969318ba67308a2d8c269c4236c6\transformed\jetified-android-voice-processor-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [ai.picovoice:android-voice-processor:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\738a969318ba67308a2d8c269c4236c6\transformed\jetified-android-voice-processor-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f74cc867abd30b8688c7ae6529065ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f74cc867abd30b8688c7ae6529065ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\22558a89f6d65b3648d7dad30bc91e1e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\22558a89f6d65b3648d7dad30bc91e1e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5374e1cacf5ffd11fca12e0695f3864d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5374e1cacf5ffd11fca12e0695f3864d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a6484b74439302b8a4570a2f24011ed\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a6484b74439302b8a4570a2f24011ed\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\215b388b83b16a90f62348e2bcbde19b\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\215b388b83b16a90f62348e2bcbde19b\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec3db78c55efdeced6819ea166fdaa03\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec3db78c55efdeced6819ea166fdaa03\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f16a10750b970ea1fc01166532608aa1\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f16a10750b970ea1fc01166532608aa1\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ca0619ccb7ea7f59335631e58e8df8d\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ca0619ccb7ea7f59335631e58e8df8d\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78c6b648ed446034e57417531717e962\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\78c6b648ed446034e57417531717e962\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\b58c2cad4f3d07c5d5d69dcfa1d96289\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\b58c2cad4f3d07c5d5d69dcfa1d96289\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cb3a926481a9678d3fb79c6ab1c379a\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cb3a926481a9678d3fb79c6ab1c379a\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f806a1774dbedb49d30e5f8663b8785\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f806a1774dbedb49d30e5f8663b8785\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c03cb0101fcf02cfeba542bac3104662\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c03cb0101fcf02cfeba542bac3104662\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\771c6f9737c7d57ce2e3bdd503381a3e\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\771c6f9737c7d57ce2e3bdd503381a3e\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\63938fb9d8f0ce47aeb6e0fa6294e058\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\63938fb9d8f0ce47aeb6e0fa6294e058\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\284bdafae067a0f3a90fa6dc08e9dcd1\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\284bdafae067a0f3a90fa6dc08e9dcd1\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b57f9381ff59d7d765a2f23afc676697\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b57f9381ff59d7d765a2f23afc676697\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\982d830af71125e23d1d43478738237a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\982d830af71125e23d1d43478738237a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb532ef1d629465f04f84e4d1d93bd30\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb532ef1d629465f04f84e4d1d93bd30\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba235b4531af3319253ff2fa28da5fd7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba235b4531af3319253ff2fa28da5fd7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b0adb4e06975a0fa31372a1fdf481bf\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\501622f65ed3684b84b48b1ba22e54b2\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\501622f65ed3684b84b48b1ba22e54b2\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\414541ca25323672fae07835fcdb26bf\transformed\jetified-tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\414541ca25323672fae07835fcdb26bf\transformed\jetified-tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\29bb4f23fe0ee5ed4b52ef8e80afeb4b\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\29bb4f23fe0ee5ed4b52ef8e80afeb4b\transformed\jetified-tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-task-text:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\47441b91c3afecee0d60009c069bdd38\transformed\jetified-tensorflow-lite-task-text-0.4.4\AndroidManifest.xml:5:5-7:41
MERGED from [org.tensorflow:tensorflow-lite-task-text:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\47441b91c3afecee0d60009c069bdd38\transformed\jetified-tensorflow-lite-task-text-0.4.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\0c6f5492bb3e44ff32a78c82023c09be\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\0c6f5492bb3e44ff32a78c82023c09be\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\6d85e2d9d46aba4b83881e1244955e01\transformed\jetified-tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\6d85e2d9d46aba4b83881e1244955e01\transformed\jetified-tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee0533333a4694945cdab2604154b76\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ee0533333a4694945cdab2604154b76\transformed\jetified-tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\70b3979cf6b38d608dc83128a5aa880d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\70b3979cf6b38d608dc83128a5aa880d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
provider#com.microsoft.cognitiveservices.speech.util.InternalContentProvider
ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:15:9-18:40
	android:authorities
		ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:16:13-108
	android:exported
		ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:18:13-37
	android:name
		ADDED from [com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:17:13-57
