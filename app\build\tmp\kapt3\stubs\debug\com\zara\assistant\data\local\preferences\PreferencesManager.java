package com.zara.assistant.data.local.preferences;

/**
 * Manager for handling app preferences using DataStore
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b$\b\u0007\u0018\u0000 >2\u00020\u0001:\u0001>B\u0015\b\u0007\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\u0005J\u000e\u0010\u0006\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\bJ\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\nH\u0086@\u00a2\u0006\u0002\u0010\bJ\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rJ\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\rJ\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\rJ\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00120\rJ\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\rJ\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\rJ\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\rJ\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00120\rJ\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u001b0\rJ\u0016\u0010\"\u001a\u00020\u00072\u0006\u0010#\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010$J\u0016\u0010%\u001a\u00020\u00072\u0006\u0010&\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\'J\u0016\u0010(\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010+\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010,\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010-\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010*J\u000e\u0010.\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010/\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u00102J\u0016\u00103\u001a\u00020\u00072\u0006\u00104\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u00102J\u0016\u00105\u001a\u00020\u00072\u0006\u00106\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u00107J\u0016\u00108\u001a\u00020\u00072\u0006\u00109\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010:J\u0016\u0010;\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010<\u001a\u00020\u00072\u0006\u0010=\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u00102R\u0014\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/zara/assistant/data/local/preferences/PreferencesManager;", "", "dataStore", "Landroidx/datastore/core/DataStore;", "Landroidx/datastore/preferences/core/Preferences;", "(Landroidx/datastore/core/DataStore;)V", "clearAllPreferences", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportPreferences", "", "", "getAIPersonality", "Lkotlinx/coroutines/flow/Flow;", "Lcom/zara/assistant/core/Constants$AIPersonality;", "getAIResponseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "getSpeechPitch", "", "getSpeechRate", "getThemeMode", "Lcom/zara/assistant/domain/repository/ThemeMode;", "getVoiceLanguage", "getVoiceSettings", "Lcom/zara/assistant/domain/model/VoiceSettings;", "getWakeWordSensitivity", "isAccessibilityServiceEnabled", "", "isAnalyticsEnabled", "isAutoListenEnabled", "isConversationHistoryEnabled", "isFirstLaunch", "isNotificationAccessEnabled", "isWakeWordEnabled", "setAIPersonality", "personality", "(Lcom/zara/assistant/core/Constants$AIPersonality;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAIResponseStyle", "style", "(Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAccessibilityServiceEnabled", "enabled", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAnalyticsEnabled", "setAutoListen", "setConversationHistoryEnabled", "setFirstLaunchCompleted", "setNotificationAccessEnabled", "setSpeechPitch", "pitch", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setSpeechRate", "rate", "setThemeMode", "mode", "(Lcom/zara/assistant/domain/repository/ThemeMode;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setVoiceLanguage", "language", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setWakeWordEnabled", "setWakeWordSensitivity", "sensitivity", "Companion", "app_debug"})
public final class PreferencesManager {
    @org.jetbrains.annotations.NotNull()
    private final androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> dataStore = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> WAKE_WORD_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Float> WAKE_WORD_SENSITIVITY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Float> SPEECH_RATE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Float> SPEECH_PITCH = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> VOICE_LANGUAGE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> AUTO_LISTEN = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> AI_PERSONALITY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> AI_RESPONSE_STYLE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> CONVERSATION_HISTORY_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> ANALYTICS_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> FIRST_LAUNCH = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> ACCESSIBILITY_SERVICE_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> NOTIFICATION_ACCESS_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> THEME_MODE = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.data.local.preferences.PreferencesManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public PreferencesManager(@org.jetbrains.annotations.NotNull()
    androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> dataStore) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setWakeWordEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isWakeWordEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setWakeWordSensitivity(float sensitivity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Float> getWakeWordSensitivity() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setSpeechRate(float rate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Float> getSpeechRate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setSpeechPitch(float pitch, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Float> getSpeechPitch() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setVoiceLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getVoiceLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAutoListen(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isAutoListenEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.VoiceSettings> getVoiceSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAIPersonality(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIPersonality personality, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.zara.assistant.core.Constants.AIPersonality> getAIPersonality() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAIResponseStyle(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIResponseStyle style, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.zara.assistant.core.Constants.AIResponseStyle> getAIResponseStyle() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setConversationHistoryEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isConversationHistoryEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAnalyticsEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isAnalyticsEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setFirstLaunchCompleted(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isFirstLaunch() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAccessibilityServiceEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isAccessibilityServiceEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setNotificationAccessEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isNotificationAccessEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setThemeMode(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.ThemeMode mode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.repository.ThemeMode> getThemeMode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object exportPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010\u0007\n\u0002\b\f\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0007R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0007R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0007R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0007R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0007R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0007R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0007R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0007R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00180\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0007R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0007R\u0017\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0007R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0007R\u0017\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00180\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0007\u00a8\u0006$"}, d2 = {"Lcom/zara/assistant/data/local/preferences/PreferencesManager$Companion;", "", "()V", "ACCESSIBILITY_SERVICE_ENABLED", "Landroidx/datastore/preferences/core/Preferences$Key;", "", "getACCESSIBILITY_SERVICE_ENABLED", "()Landroidx/datastore/preferences/core/Preferences$Key;", "AI_PERSONALITY", "", "getAI_PERSONALITY", "AI_RESPONSE_STYLE", "getAI_RESPONSE_STYLE", "ANALYTICS_ENABLED", "getANALYTICS_ENABLED", "AUTO_LISTEN", "getAUTO_LISTEN", "CONVERSATION_HISTORY_ENABLED", "getCONVERSATION_HISTORY_ENABLED", "FIRST_LAUNCH", "getFIRST_LAUNCH", "NOTIFICATION_ACCESS_ENABLED", "getNOTIFICATION_ACCESS_ENABLED", "SPEECH_PITCH", "", "getSPEECH_PITCH", "SPEECH_RATE", "getSPEECH_RATE", "THEME_MODE", "getTHEME_MODE", "VOICE_LANGUAGE", "getVOICE_LANGUAGE", "WAKE_WORD_ENABLED", "getWAKE_WORD_ENABLED", "WAKE_WORD_SENSITIVITY", "getWAKE_WORD_SENSITIVITY", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getWAKE_WORD_ENABLED() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Float> getWAKE_WORD_SENSITIVITY() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Float> getSPEECH_RATE() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Float> getSPEECH_PITCH() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> getVOICE_LANGUAGE() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getAUTO_LISTEN() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> getAI_PERSONALITY() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> getAI_RESPONSE_STYLE() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getCONVERSATION_HISTORY_ENABLED() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getANALYTICS_ENABLED() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getFIRST_LAUNCH() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getACCESSIBILITY_SERVICE_ENABLED() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> getNOTIFICATION_ACCESS_ENABLED() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> getTHEME_MODE() {
            return null;
        }
    }
}