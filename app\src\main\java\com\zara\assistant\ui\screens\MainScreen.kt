package com.zara.assistant.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Help
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.outlined.VolumeUp
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.zara.assistant.R
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.presentation.components.VoiceButton
import com.zara.assistant.presentation.components.VoiceVisualization
import com.zara.assistant.presentation.theme.GradientPrimary
import com.zara.assistant.presentation.theme.TextSecondaryLight
import com.zara.assistant.presentation.theme.VoiceListening
import com.zara.assistant.presentation.theme.VoiceProcessing
import com.zara.assistant.presentation.theme.VoiceSpeaking
import com.zara.assistant.ui.viewmodel.MainUiState
import com.zara.assistant.ui.viewmodel.MainViewModel
import com.zara.assistant.presentation.utils.AccessibilityUtils
import com.zara.assistant.presentation.utils.PerformanceUtils

/**
 * Main screen of the Zara AI Voice Assistant
 */
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onNavigateToSettings: () -> Unit,
    onNavigateToCommands: () -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Show error messages
    LaunchedEffect(uiState.errorMessage) {
        uiState.errorMessage?.let { message ->
            snackbarHostState.showSnackbar(
                message = message,
                duration = androidx.compose.material3.SnackbarDuration.Short
            )
        }
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.background,
                            MaterialTheme.colorScheme.surface
                        )
                    )
                )
                .padding(paddingValues)
        ) {
            // Main content with modern layout
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .statusBarsPadding()
                    .padding(horizontal = 24.dp, vertical = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Enhanced top section
                ModernTopSection(
                    onSettingsClick = onNavigateToSettings,
                    onCommandsClick = onNavigateToCommands
                )

                Spacer(modifier = Modifier.height(32.dp))

                // Enhanced center section with voice interaction
                ModernCenterSection(
                    uiState = uiState,
                    onVoiceButtonClick = { viewModel.onVoiceButtonClicked() },
                    modifier = Modifier.weight(1f)
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Enhanced bottom section with modern controls
                ModernBottomSection(
                    uiState = uiState,
                    onWakeWordToggle = { enabled -> viewModel.onWakeWordToggle(enabled) }
                )
            }

            // Permission dialog
            if (uiState.showPermissionDialog) {
                PermissionDialog(
                    missingPermissions = uiState.missingPermissions,
                    onRequestPermissions = { /* Handle permission request */ },
                    onDismiss = { viewModel.dismissPermissionDialog() }
                )
            }
        }
    }
}

@Composable
private fun ModernTopSection(
    onSettingsClick: () -> Unit,
    onCommandsClick: () -> Unit
) {
    NeumorphismCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = 8.dp,
        contentPadding = 16.dp
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Enhanced Zara branding
            Column {
                Text(
                    text = "Zara",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "AI Voice Assistant",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }

            // Modern action buttons with neumorphism
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NeumorphismCard(
                    modifier = Modifier.size(48.dp),
                    elevation = 4.dp,
                    contentPadding = 0.dp
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clickable { onCommandsClick() },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Help,
                            contentDescription = "Voice Commands",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                NeumorphismCard(
                    modifier = Modifier.size(48.dp),
                    elevation = 4.dp,
                    contentPadding = 0.dp
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clickable { onSettingsClick() },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = stringResource(R.string.settings),
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ModernCenterSection(
    uiState: MainUiState,
    onVoiceButtonClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Enhanced voice visualization with modern animations
        AnimatedVisibility(
            visible = uiState.voiceState.isListening ||
                     uiState.voiceState.isProcessing ||
                     uiState.voiceState.isSpeaking,
            enter = scaleIn(animationSpec = tween(300)) + fadeIn(),
            exit = scaleOut(animationSpec = tween(300)) + fadeOut()
        ) {
            NeumorphismCard(
                modifier = Modifier.padding(bottom = 40.dp),
                elevation = 12.dp,
                contentPadding = 24.dp
            ) {
                VoiceVisualization(
                    voiceState = mapToConstantsVoiceState(uiState.voiceState.currentState),
                    modifier = Modifier.size(width = 200.dp, height = 80.dp)
                )
            }
        }

        // Enhanced main voice button with glow effect and accessibility
        val buttonScale by animateFloatAsState(
            targetValue = if (uiState.voiceState.isListening) 1.1f else 1f,
            animationSpec = tween(PerformanceUtils.adaptiveAnimationDuration(300)),
            label = "buttonScale"
        )

        Box(
            contentAlignment = Alignment.Center
        ) {
            // Glow effect background (reduced for low performance devices)
            androidx.compose.animation.AnimatedVisibility(
                visible = uiState.voiceState.isListening ||
                         uiState.voiceState.isProcessing ||
                         uiState.voiceState.isSpeaking,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .size(if (PerformanceUtils.isLowPerformanceDevice()) 160.dp else 180.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    when {
                                        uiState.voiceState.isListening -> VoiceListening.copy(alpha = 0.3f)
                                        uiState.voiceState.isProcessing -> VoiceProcessing.copy(alpha = 0.3f)
                                        uiState.voiceState.isSpeaking -> VoiceSpeaking.copy(alpha = 0.3f)
                                        else -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                                    },
                                    androidx.compose.ui.graphics.Color.Transparent
                                ),
                                radius = 300f
                            ),
                            shape = CircleShape
                        )
                )
            }

            // Main voice button with accessibility
            VoiceButton(
                voiceState = mapToConstantsVoiceState(uiState.voiceState.currentState),
                onClick = onVoiceButtonClick,
                modifier = Modifier
                    .size(140.dp)
                    .scale(buttonScale)
                    .semantics {
                        contentDescription = AccessibilityUtils.getVoiceStateDescription(
                            isListening = uiState.voiceState.isListening,
                            isProcessing = uiState.voiceState.isProcessing,
                            isSpeaking = uiState.voiceState.isSpeaking
                        )
                        role = androidx.compose.ui.semantics.Role.Button
                    }
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Enhanced voice state display
        NeumorphismCard(
            elevation = 6.dp,
            contentPadding = 20.dp
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = uiState.voiceStateText,
                    style = MaterialTheme.typography.titleMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        // Current command display
        uiState.voiceState.currentCommand?.let { command ->
            Spacer(modifier = Modifier.height(16.dp))
            NeumorphismCard(
                modifier = Modifier.fillMaxWidth(),
                contentPadding = 16.dp
            ) {
                Text(
                    text = "\"$command\"",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun ModernBottomSection(
    uiState: MainUiState,
    onWakeWordToggle: (Boolean) -> Unit
) {
    NeumorphismCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = 10.dp,
        contentPadding = 20.dp
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Enhanced wake word toggle with status indicator
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Status indicator
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = if (uiState.isWakeWordActive) {
                                    VoiceListening
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                                },
                                shape = CircleShape
                            )
                    )

                    Column {
                        Text(
                            text = "Hey Zara",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = if (uiState.isWakeWordActive) {
                                "Listening for wake word"
                            } else {
                                "Wake word disabled"
                            },
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }

                Switch(
                    checked = uiState.isWakeWordEnabled,
                    onCheckedChange = onWakeWordToggle
                )
            }

            // Modern service status indicators
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Voice service status
                StatusIndicator(
                    icon = Icons.Filled.Mic,
                    label = "Voice",
                    isActive = uiState.voiceState.isListening || uiState.voiceState.isProcessing,
                    modifier = Modifier.weight(1f)
                )

                // Wake word service status
                StatusIndicator(
                    icon = Icons.Outlined.VolumeUp,
                    label = "Wake Word",
                    isActive = uiState.isWakeWordServiceRunning,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun StatusIndicator(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    isActive: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .background(
                    color = if (isActive) {
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.05f)
                    },
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = if (isActive) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                },
                modifier = Modifier.size(20.dp)
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = if (isActive) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            }
        )
    }
}

@Composable
private fun PermissionDialog(
    missingPermissions: List<String>,
    onRequestPermissions: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(text = "Permissions Required")
        },
        text = {
            Column {
                Text(text = "Zara needs the following permissions to work properly:")
                Spacer(modifier = Modifier.height(8.dp))
                missingPermissions.forEach { permission ->
                    Text(
                        text = "• ${getPermissionDisplayName(permission)}",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onRequestPermissions) {
                Text("Grant Permissions")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

private fun getPermissionDisplayName(permission: String): String {
    return when (permission) {
        android.Manifest.permission.RECORD_AUDIO -> "Microphone access"
        android.Manifest.permission.POST_NOTIFICATIONS -> "Notification access"
        android.Manifest.permission.SYSTEM_ALERT_WINDOW -> "Display over other apps"
        android.Manifest.permission.CALL_PHONE -> "Phone access"
        android.Manifest.permission.SEND_SMS -> "SMS access"
        android.Manifest.permission.READ_CONTACTS -> "Contacts access"
        android.Manifest.permission.ACCESS_FINE_LOCATION -> "Location access"
        android.Manifest.permission.ACCESS_COARSE_LOCATION -> "Location access"
        else -> permission
    }
}

private fun mapToConstantsVoiceState(state: com.zara.assistant.domain.model.VoiceState.State): Constants.VoiceState {
    return when (state) {
        com.zara.assistant.domain.model.VoiceState.State.IDLE -> Constants.VoiceState.IDLE
        com.zara.assistant.domain.model.VoiceState.State.LISTENING_WAKE_WORD -> Constants.VoiceState.LISTENING_WAKE_WORD
        com.zara.assistant.domain.model.VoiceState.State.LISTENING_COMMAND -> Constants.VoiceState.LISTENING_COMMAND
        com.zara.assistant.domain.model.VoiceState.State.PROCESSING_COMMAND -> Constants.VoiceState.PROCESSING
        com.zara.assistant.domain.model.VoiceState.State.GENERATING_RESPONSE -> Constants.VoiceState.PROCESSING
        com.zara.assistant.domain.model.VoiceState.State.SPEAKING_RESPONSE -> Constants.VoiceState.SPEAKING
        com.zara.assistant.domain.model.VoiceState.State.EXECUTING_ACTION -> Constants.VoiceState.PROCESSING
        com.zara.assistant.domain.model.VoiceState.State.ERROR -> Constants.VoiceState.ERROR
        com.zara.assistant.domain.model.VoiceState.State.DISABLED -> Constants.VoiceState.IDLE
    }
}
