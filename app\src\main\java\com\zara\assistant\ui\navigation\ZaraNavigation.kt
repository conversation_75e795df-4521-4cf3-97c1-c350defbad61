package com.zara.assistant.ui.navigation

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.zara.assistant.ui.screens.AboutScreen
import com.zara.assistant.ui.screens.CommandsHelpScreen
import com.zara.assistant.ui.screens.MainScreen
import com.zara.assistant.ui.screens.OnboardingScreen
import com.zara.assistant.ui.screens.PermissionsScreen
import com.zara.assistant.ui.screens.SettingsScreen
import com.zara.assistant.ui.viewmodel.MainViewModel
import com.zara.assistant.ui.viewmodel.SettingsViewModel

/**
 * Main navigation component for Zara app
 */
@Composable
fun ZaraNavigation(
    mainViewModel: MainViewModel,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    startDestination: String = ZaraDestinations.MAIN_ROUTE
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
        enterTransition = {
            slideInHorizontally(
                initialOffsetX = { it },
                animationSpec = tween(300)
            ) + fadeIn(animationSpec = tween(300))
        },
        exitTransition = {
            slideOutHorizontally(
                targetOffsetX = { -it },
                animationSpec = tween(300)
            ) + fadeOut(animationSpec = tween(300))
        },
        popEnterTransition = {
            slideInHorizontally(
                initialOffsetX = { -it },
                animationSpec = tween(300)
            ) + fadeIn(animationSpec = tween(300))
        },
        popExitTransition = {
            slideOutHorizontally(
                targetOffsetX = { it },
                animationSpec = tween(300)
            ) + fadeOut(animationSpec = tween(300))
        }
    ) {
        composable(
            route = ZaraDestinations.ONBOARDING_ROUTE,
            enterTransition = {
                fadeIn(animationSpec = tween(500))
            },
            exitTransition = {
                fadeOut(animationSpec = tween(300))
            }
        ) {
            OnboardingScreen(
                onNavigateToPermissions = {
                    navController.navigate(ZaraDestinations.PERMISSIONS_ROUTE)
                },
                onNavigateToMain = {
                    mainViewModel.completeOnboarding()
                    navController.navigate(ZaraDestinations.MAIN_ROUTE) {
                        popUpTo(ZaraDestinations.ONBOARDING_ROUTE) { inclusive = true }
                    }
                }
            )
        }

        composable(
            route = ZaraDestinations.PERMISSIONS_ROUTE,
            enterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(400)
                ) + fadeIn(animationSpec = tween(400))
            },
            exitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { -it },
                    animationSpec = tween(400)
                ) + fadeOut(animationSpec = tween(400))
            }
        ) {
            PermissionsScreen(
                onPermissionsGranted = {
                    navController.navigate(ZaraDestinations.MAIN_ROUTE) {
                        popUpTo(ZaraDestinations.PERMISSIONS_ROUTE) { inclusive = true }
                    }
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable(
            route = ZaraDestinations.MAIN_ROUTE,
            enterTransition = {
                fadeIn(animationSpec = tween(400))
            },
            exitTransition = {
                fadeOut(animationSpec = tween(300))
            }
        ) {
            val viewModel: MainViewModel = hiltViewModel()
            MainScreen(
                viewModel = viewModel,
                onNavigateToSettings = {
                    navController.navigate(ZaraDestinations.SETTINGS_ROUTE)
                },
                onNavigateToCommands = {
                    navController.navigate(ZaraDestinations.COMMANDS_HELP_ROUTE)
                }
            )
        }
        
        composable(
            route = ZaraDestinations.SETTINGS_ROUTE,
            enterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeIn(animationSpec = tween(350))
            },
            exitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeOut(animationSpec = tween(350))
            },
            popEnterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeIn(animationSpec = tween(350))
            },
            popExitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeOut(animationSpec = tween(350))
            }
        ) {
            val viewModel: SettingsViewModel = hiltViewModel()
            SettingsScreen(
                viewModel = viewModel,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToAbout = {
                    navController.navigate(ZaraDestinations.ABOUT_ROUTE)
                }
            )
        }

        composable(
            route = ZaraDestinations.COMMANDS_HELP_ROUTE,
            enterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeIn(animationSpec = tween(350))
            },
            exitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeOut(animationSpec = tween(350))
            },
            popEnterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeIn(animationSpec = tween(350))
            },
            popExitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeOut(animationSpec = tween(350))
            }
        ) {
            CommandsHelpScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable(
            route = ZaraDestinations.ABOUT_ROUTE,
            enterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeIn(animationSpec = tween(350))
            },
            exitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeOut(animationSpec = tween(350))
            },
            popEnterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeIn(animationSpec = tween(350))
            },
            popExitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(350)
                ) + fadeOut(animationSpec = tween(350))
            }
        ) {
            AboutScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}

/**
 * Destinations used in the Zara app
 */
object ZaraDestinations {
    const val ONBOARDING_ROUTE = "onboarding"
    const val PERMISSIONS_ROUTE = "permissions"
    const val MAIN_ROUTE = "main"
    const val SETTINGS_ROUTE = "settings"
    const val COMMANDS_HELP_ROUTE = "commands_help"
    const val ABOUT_ROUTE = "about"
}
