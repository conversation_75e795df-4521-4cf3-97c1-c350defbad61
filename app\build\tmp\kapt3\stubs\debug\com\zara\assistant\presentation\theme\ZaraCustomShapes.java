package com.zara.assistant.presentation.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0011\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0006R\u0011\u0010\t\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0006R\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0006R\u0011\u0010\r\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0006R\u0011\u0010\u000f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0006R\u0011\u0010\u0011\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0006R\u0011\u0010\u0013\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0006\u00a8\u0006\u0015"}, d2 = {"Lcom/zara/assistant/presentation/theme/ZaraCustomShapes;", "", "()V", "BottomSheet", "Landroidx/compose/foundation/shape/RoundedCornerShape;", "getBottomSheet", "()Landroidx/compose/foundation/shape/RoundedCornerShape;", "Chip", "getChip", "Dialog", "getDialog", "FloatingCard", "getFloatingCard", "NeumorphismButton", "getNeumorphismButton", "NeumorphismCard", "getNeumorphismCard", "ProgressBar", "getProgressBar", "VoiceButton", "getVoiceButton", "app_debug"})
public final class ZaraCustomShapes {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape VoiceButton = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape NeumorphismCard = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape NeumorphismButton = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape FloatingCard = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape BottomSheet = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape Dialog = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape Chip = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.foundation.shape.RoundedCornerShape ProgressBar = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.presentation.theme.ZaraCustomShapes INSTANCE = null;
    
    private ZaraCustomShapes() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getVoiceButton() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getNeumorphismCard() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getNeumorphismButton() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getFloatingCard() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getBottomSheet() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getChip() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.foundation.shape.RoundedCornerShape getProgressBar() {
        return null;
    }
}