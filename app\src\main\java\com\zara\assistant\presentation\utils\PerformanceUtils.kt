package com.zara.assistant.presentation.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

/**
 * Performance optimization utilities for Zara UI components
 */
object PerformanceUtils {
    
    /**
     * Debounce function for expensive operations
     */
    @Composable
    fun <T> rememberDebounced(
        value: T,
        delayMillis: Long = 300L
    ): T {
        var debouncedValue by remember { mutableStateOf(value) }
        
        LaunchedEffect(value) {
            delay(delayMillis)
            debouncedValue = value
        }
        
        return debouncedValue
    }
    
    /**
     * Throttle function for frequent UI updates
     */
    @Composable
    fun <T> rememberThrottled(
        value: T,
        intervalMillis: Long = 100L
    ): T {
        var throttledValue by remember { mutableStateOf(value) }
        var lastUpdateTime by remember { mutableStateOf(0L) }
        
        LaunchedEffect(value) {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastUpdateTime >= intervalMillis) {
                throttledValue = value
                lastUpdateTime = currentTime
            }
        }
        
        return throttledValue
    }
    
    /**
     * Check if device is in low performance mode
     */
    @Composable
    fun isLowPerformanceDevice(): Boolean {
        val view = LocalView.current
        return remember {
            // Simple heuristic based on available memory and CPU
            val runtime = Runtime.getRuntime()
            val maxMemory = runtime.maxMemory()
            val availableProcessors = runtime.availableProcessors()
            
            // Consider low performance if less than 2GB RAM or less than 4 cores
            maxMemory < 2L * 1024 * 1024 * 1024 || availableProcessors < 4
        }
    }
    
    /**
     * Adaptive elevation based on performance
     */
    @Composable
    fun adaptiveElevation(baseElevation: Dp): Dp {
        val isLowPerf = isLowPerformanceDevice()
        return if (isLowPerf) baseElevation * 0.5f else baseElevation
    }
    
    /**
     * Adaptive animation duration based on performance
     */
    @Composable
    fun adaptiveAnimationDuration(baseDuration: Int): Int {
        val isLowPerf = isLowPerformanceDevice()
        return if (isLowPerf) (baseDuration * 0.7f).toInt() else baseDuration
    }
}

/**
 * Accessibility utilities for better user experience
 */
object AccessibilityUtils {
    
    /**
     * Generate semantic description for voice states
     */
    fun getVoiceStateDescription(isListening: Boolean, isProcessing: Boolean, isSpeaking: Boolean): String {
        return when {
            isListening -> "Zara is listening for your command"
            isProcessing -> "Zara is processing your request"
            isSpeaking -> "Zara is speaking"
            else -> "Tap to activate Zara voice assistant"
        }
    }
    
    /**
     * Generate semantic description for settings
     */
    fun getSettingDescription(title: String, isEnabled: Boolean): String {
        return "$title is ${if (isEnabled) "enabled" else "disabled"}"
    }
    
    /**
     * Generate semantic description for sliders
     */
    fun getSliderDescription(title: String, value: Float, range: ClosedFloatingPointRange<Float>): String {
        val percentage = ((value - range.start) / (range.endInclusive - range.start) * 100).toInt()
        return "$title is set to $percentage percent"
    }
}

/**
 * Memory optimization utilities
 */
object MemoryUtils {
    
    /**
     * Suggest garbage collection if memory is low
     */
    fun suggestGCIfNeeded() {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toDouble() / maxMemory.toDouble()) * 100
        
        if (memoryUsagePercent > 80) {
            System.gc()
        }
    }
    
    /**
     * Get memory usage information
     */
    fun getMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val freeMemory = maxMemory - usedMemory
        
        return MemoryInfo(
            usedMemory = usedMemory,
            freeMemory = freeMemory,
            maxMemory = maxMemory,
            usagePercent = (usedMemory.toDouble() / maxMemory.toDouble()) * 100
        )
    }
}

data class MemoryInfo(
    val usedMemory: Long,
    val freeMemory: Long,
    val maxMemory: Long,
    val usagePercent: Double
)
