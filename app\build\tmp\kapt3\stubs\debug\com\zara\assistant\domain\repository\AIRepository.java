package com.zara.assistant.domain.repository;

/**
 * Repository interface for AI-related operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0003\n\u0002\b\u0010\bf\u0018\u00002\u00020\u0001J4\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ\u001e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u0010H\u00a6@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0015\u0010\u0016J\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0018\u0010\u0016J$\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00132\u0006\u0010\u001b\u001a\u00020\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u000e\u0010\u001e\u001a\u00020\rH\u00a6@\u00a2\u0006\u0002\u0010\u0016J\u001c\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u00a6@\u00a2\u0006\u0002\u0010!J8\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00030\u00132\u0006\u0010#\u001a\u00020\u00032\b\b\u0002\u0010$\u001a\u00020%2\b\b\u0002\u0010&\u001a\u00020\'H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b(\u0010)JH\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00100\u00132\u0006\u0010\u0004\u001a\u00020+2\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b,\u0010-J\u0018\u0010.\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u000e\u001a\u00020\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u001dJ\u0016\u0010/\u001a\u00020\u00102\u0006\u0010\u0004\u001a\u00020+H\u00a6@\u00a2\u0006\u0002\u00100J\u000e\u00101\u001a\b\u0012\u0004\u0012\u00020302H&J\u001e\u00104\u001a\u00020\u00102\u0006\u00105\u001a\u0002062\u0006\u0010\u0004\u001a\u00020+H\u00a6@\u00a2\u0006\u0002\u00107J$\u00108\u001a\b\u0012\u0004\u0012\u00020\u00100\u00132\u0006\u0010\u0004\u001a\u00020+H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b9\u00100J.\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00030\u00132\u0006\u0010;\u001a\u00020\u00032\b\b\u0002\u0010$\u001a\u00020%H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b<\u0010=J\u0016\u0010>\u001a\u00020\r2\u0006\u0010?\u001a\u000203H\u00a6@\u00a2\u0006\u0002\u0010@J\u0016\u0010A\u001a\b\u0012\u0004\u0012\u00020\u0003022\u0006\u0010\u0004\u001a\u00020+H&J*\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00030\u00132\f\u0010C\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bD\u0010E\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006F"}, d2 = {"Lcom/zara/assistant/domain/repository/AIRepository;", "", "buildContextPrompt", "", "command", "conversationHistory", "", "personality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "responseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "(Ljava/lang/String;Ljava/util/List;Lcom/zara/assistant/core/Constants$AIPersonality;Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheResponse", "", "commandHash", "response", "Lcom/zara/assistant/domain/model/AIResponse;", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/AIResponse;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkCohereHealth", "Lkotlin/Result;", "", "checkCohereHealth-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkPerplexityHealth", "checkPerplexityHealth-IoAF18A", "classifyCommand", "Lcom/zara/assistant/domain/model/CommandType;", "text", "classifyCommand-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearResponseCache", "extractSystemActions", "Lcom/zara/assistant/domain/model/SystemAction;", "(Lcom/zara/assistant/domain/model/AIResponse;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateCohereResponse", "prompt", "maxTokens", "", "temperature", "", "generateCohereResponse-BWLJW6A", "(Ljava/lang/String;IFLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateResponse", "Lcom/zara/assistant/domain/model/VoiceCommand;", "generateResponse-yxL6bBk", "(Lcom/zara/assistant/domain/model/VoiceCommand;Ljava/util/List;Lcom/zara/assistant/core/Constants$AIPersonality;Lcom/zara/assistant/core/Constants$AIResponseStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCachedResponse", "getFallbackResponse", "(Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPreferredAISource", "Lkotlinx/coroutines/flow/Flow;", "Lcom/zara/assistant/domain/model/AISource;", "handleAPIError", "error", "", "(Ljava/lang/Throwable;Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processCommand", "processCommand-gIAlu-s", "searchPerplexity", "query", "searchPerplexity-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setPreferredAISource", "source", "(Lcom/zara/assistant/domain/model/AISource;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "streamResponse", "summarizeConversation", "messages", "summarizeConversation-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface AIRepository {
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object extractSystemActions(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.AIResponse response, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.SystemAction>> $completion);
    
    /**
     * Context and conversation management
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object buildContextPrompt(@org.jetbrains.annotations.NotNull()
    java.lang.String command, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> conversationHistory, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIPersonality personality, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIResponseStyle responseStyle, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion);
    
    /**
     * Response caching
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCachedResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String commandHash, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object cacheResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String commandHash, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.AIResponse response, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearResponseCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.zara.assistant.domain.model.AISource> getPreferredAISource();
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setPreferredAISource(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.AISource source, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Response streaming
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.String> streamResponse(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceCommand command);
    
    /**
     * Error handling and fallbacks
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFallbackResponse(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceCommand command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object handleAPIError(@org.jetbrains.annotations.NotNull()
    java.lang.Throwable error, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceCommand command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.AIResponse> $completion);
    
    /**
     * Repository interface for AI-related operations
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}