package com.zara.assistant.domain.model;

/**
 * Command execution result
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J=\u0010\u0017\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00032\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001d"}, d2 = {"Lcom/zara/assistant/domain/model/CommandResult;", "", "isComplete", "", "needsClarification", "clarificationQuestion", "", "parameters", "Lcom/zara/assistant/domain/model/CommandParameters;", "canExecute", "(ZZLjava/lang/String;Lcom/zara/assistant/domain/model/CommandParameters;Z)V", "getCanExecute", "()Z", "getClarificationQuestion", "()Ljava/lang/String;", "getNeedsClarification", "getParameters", "()Lcom/zara/assistant/domain/model/CommandParameters;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class CommandResult {
    private final boolean isComplete = false;
    private final boolean needsClarification = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String clarificationQuestion = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.CommandParameters parameters = null;
    private final boolean canExecute = false;
    
    public CommandResult(boolean isComplete, boolean needsClarification, @org.jetbrains.annotations.Nullable()
    java.lang.String clarificationQuestion, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.CommandParameters parameters, boolean canExecute) {
        super();
    }
    
    public final boolean isComplete() {
        return false;
    }
    
    public final boolean getNeedsClarification() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getClarificationQuestion() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters getParameters() {
        return null;
    }
    
    public final boolean getCanExecute() {
        return false;
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandParameters component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.CommandResult copy(boolean isComplete, boolean needsClarification, @org.jetbrains.annotations.Nullable()
    java.lang.String clarificationQuestion, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.CommandParameters parameters, boolean canExecute) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}