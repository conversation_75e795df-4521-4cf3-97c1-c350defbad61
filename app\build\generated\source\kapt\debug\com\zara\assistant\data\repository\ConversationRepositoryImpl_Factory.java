// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.data.repository;

import com.google.gson.Gson;
import com.zara.assistant.data.local.dao.ConversationDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ConversationRepositoryImpl_Factory implements Factory<ConversationRepositoryImpl> {
  private final Provider<ConversationDao> conversationDaoProvider;

  private final Provider<Gson> gsonProvider;

  public ConversationRepositoryImpl_Factory(Provider<ConversationDao> conversationDaoProvider,
      Provider<Gson> gsonProvider) {
    this.conversationDaoProvider = conversationDaoProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public ConversationRepositoryImpl get() {
    return newInstance(conversationDaoProvider.get(), gsonProvider.get());
  }

  public static ConversationRepositoryImpl_Factory create(
      Provider<ConversationDao> conversationDaoProvider, Provider<Gson> gsonProvider) {
    return new ConversationRepositoryImpl_Factory(conversationDaoProvider, gsonProvider);
  }

  public static ConversationRepositoryImpl newInstance(ConversationDao conversationDao, Gson gson) {
    return new ConversationRepositoryImpl(conversationDao, gson);
  }
}
