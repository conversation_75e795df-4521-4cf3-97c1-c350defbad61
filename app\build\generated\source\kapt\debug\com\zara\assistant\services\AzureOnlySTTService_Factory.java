// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.utils.ApiKeyManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AzureOnlySTTService_Factory implements Factory<AzureOnlySTTService> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiKeyManager> apiKeyManagerProvider;

  public AzureOnlySTTService_Factory(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider) {
    this.contextProvider = contextProvider;
    this.apiKeyManagerProvider = apiKeyManagerProvider;
  }

  @Override
  public AzureOnlySTTService get() {
    return newInstance(contextProvider.get(), apiKeyManagerProvider.get());
  }

  public static AzureOnlySTTService_Factory create(Provider<Context> contextProvider,
      Provider<ApiKeyManager> apiKeyManagerProvider) {
    return new AzureOnlySTTService_Factory(contextProvider, apiKeyManagerProvider);
  }

  public static AzureOnlySTTService newInstance(Context context, ApiKeyManager apiKeyManager) {
    return new AzureOnlySTTService(context, apiKeyManager);
  }
}
