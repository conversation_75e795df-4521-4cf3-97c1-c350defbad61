{"logs": [{"outputFile": "com.zara.assistant.app-mergeDebugResources-69:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e9dc2ee2702f1f73e551894123196f3\\transformed\\work-runtime-2.9.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47494bae0d5af340ce384dde3add152e\\transformed\\appcompat-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,41,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2572,2796,2911,3018,3131", "endLines": "4,5,6,7,21,35,36,37,40,44,45,46,47,51", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2567,2791,2906,3013,3126,3356"}}]}]}