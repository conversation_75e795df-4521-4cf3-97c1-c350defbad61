package com.zara.assistant.ui.viewmodel;

/**
 * UI state for the settings screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b%\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u008b\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\u0002\u0010\u0015J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0003H\u00c6\u0003J\t\u0010*\u001a\u00020\u0011H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0013H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0013H\u00c6\u0003J\t\u0010-\u001a\u00020\u0005H\u00c6\u0003J\t\u0010.\u001a\u00020\u0005H\u00c6\u0003J\t\u0010/\u001a\u00020\u0005H\u00c6\u0003J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\t\u00101\u001a\u00020\nH\u00c6\u0003J\t\u00102\u001a\u00020\fH\u00c6\u0003J\t\u00103\u001a\u00020\u0003H\u00c6\u0003J\t\u00104\u001a\u00020\u0003H\u00c6\u0003J\u008f\u0001\u00105\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0013H\u00c6\u0001J\u0013\u00106\u001a\u00020\u00032\b\u00107\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00108\u001a\u000209H\u00d6\u0001J\t\u0010:\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001bR\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001bR\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u001bR\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001fR\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\"R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\"\u00a8\u0006;"}, d2 = {"Lcom/zara/assistant/ui/viewmodel/SettingsUiState;", "", "isWakeWordEnabled", "", "wakeWordSensitivity", "", "speechRate", "speechPitch", "autoListen", "aiPersonality", "Lcom/zara/assistant/core/Constants$AIPersonality;", "aiResponseStyle", "Lcom/zara/assistant/core/Constants$AIResponseStyle;", "conversationHistoryEnabled", "voiceDataStorageEnabled", "analyticsEnabled", "themeMode", "Lcom/zara/assistant/domain/repository/ThemeMode;", "message", "", "errorMessage", "(ZFFFZLcom/zara/assistant/core/Constants$AIPersonality;Lcom/zara/assistant/core/Constants$AIResponseStyle;ZZZLcom/zara/assistant/domain/repository/ThemeMode;Ljava/lang/String;Ljava/lang/String;)V", "getAiPersonality", "()Lcom/zara/assistant/core/Constants$AIPersonality;", "getAiResponseStyle", "()Lcom/zara/assistant/core/Constants$AIResponseStyle;", "getAnalyticsEnabled", "()Z", "getAutoListen", "getConversationHistoryEnabled", "getErrorMessage", "()Ljava/lang/String;", "getMessage", "getSpeechPitch", "()F", "getSpeechRate", "getThemeMode", "()Lcom/zara/assistant/domain/repository/ThemeMode;", "getVoiceDataStorageEnabled", "getWakeWordSensitivity", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class SettingsUiState {
    private final boolean isWakeWordEnabled = false;
    private final float wakeWordSensitivity = 0.0F;
    private final float speechRate = 0.0F;
    private final float speechPitch = 0.0F;
    private final boolean autoListen = false;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.core.Constants.AIPersonality aiPersonality = null;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.core.Constants.AIResponseStyle aiResponseStyle = null;
    private final boolean conversationHistoryEnabled = false;
    private final boolean voiceDataStorageEnabled = false;
    private final boolean analyticsEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.repository.ThemeMode themeMode = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String message = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    
    public SettingsUiState(boolean isWakeWordEnabled, float wakeWordSensitivity, float speechRate, float speechPitch, boolean autoListen, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIPersonality aiPersonality, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIResponseStyle aiResponseStyle, boolean conversationHistoryEnabled, boolean voiceDataStorageEnabled, boolean analyticsEnabled, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.ThemeMode themeMode, @org.jetbrains.annotations.Nullable()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        super();
    }
    
    public final boolean isWakeWordEnabled() {
        return false;
    }
    
    public final float getWakeWordSensitivity() {
        return 0.0F;
    }
    
    public final float getSpeechRate() {
        return 0.0F;
    }
    
    public final float getSpeechPitch() {
        return 0.0F;
    }
    
    public final boolean getAutoListen() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.core.Constants.AIPersonality getAiPersonality() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.core.Constants.AIResponseStyle getAiResponseStyle() {
        return null;
    }
    
    public final boolean getConversationHistoryEnabled() {
        return false;
    }
    
    public final boolean getVoiceDataStorageEnabled() {
        return false;
    }
    
    public final boolean getAnalyticsEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.repository.ThemeMode getThemeMode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public SettingsUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component10() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.repository.ThemeMode component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.core.Constants.AIPersonality component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.core.Constants.AIResponseStyle component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.ui.viewmodel.SettingsUiState copy(boolean isWakeWordEnabled, float wakeWordSensitivity, float speechRate, float speechPitch, boolean autoListen, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIPersonality aiPersonality, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.core.Constants.AIResponseStyle aiResponseStyle, boolean conversationHistoryEnabled, boolean voiceDataStorageEnabled, boolean analyticsEnabled, @org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.repository.ThemeMode themeMode, @org.jetbrains.annotations.Nullable()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}