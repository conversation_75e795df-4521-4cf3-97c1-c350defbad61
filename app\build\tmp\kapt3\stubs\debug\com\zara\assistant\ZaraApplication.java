package com.zara.assistant;

/**
 * Zara Application class
 * Initializes Hilt dependency injection and sets up notification channels
 */
@dagger.hilt.android.HiltAndroidApp()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 \u00142\u00020\u00012\u00020\u0002:\u0001\u0014B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\u0010\u001a\u00020\u0011H\u0002J\b\u0010\u0012\u001a\u00020\u0011H\u0002J\b\u0010\u0013\u001a\u00020\u0011H\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\u00020\u00078VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\tR\u001e\u0010\n\u001a\u00020\u000b8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000f\u00a8\u0006\u0015"}, d2 = {"Lcom/zara/assistant/ZaraApplication;", "Landroid/app/Application;", "Landroidx/work/Configuration$Provider;", "()V", "applicationScope", "Lkotlinx/coroutines/CoroutineScope;", "workManagerConfiguration", "Landroidx/work/Configuration;", "getWorkManagerConfiguration", "()Landroidx/work/Configuration;", "workerFactory", "Landroidx/hilt/work/HiltWorkerFactory;", "getWorkerFactory", "()Landroidx/hilt/work/HiltWorkerFactory;", "setWorkerFactory", "(Landroidx/hilt/work/HiltWorkerFactory;)V", "createNotificationChannels", "", "initializeVoskModel", "onCreate", "Companion", "app_debug"})
public final class ZaraApplication extends android.app.Application implements androidx.work.Configuration.Provider {
    @javax.inject.Inject()
    public androidx.hilt.work.HiltWorkerFactory workerFactory;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope applicationScope = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String WAKE_WORD_CHANNEL_ID = "wake_word_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String VOICE_PROCESSING_CHANNEL_ID = "voice_processing_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AI_RESPONSE_CHANNEL_ID = "ai_response_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SYSTEM_CONTROL_CHANNEL_ID = "system_control_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ERROR_CHANNEL_ID = "error_channel";
    public static final int WAKE_WORD_NOTIFICATION_ID = 1001;
    public static final int VOICE_PROCESSING_NOTIFICATION_ID = 1002;
    public static final int AI_RESPONSE_NOTIFICATION_ID = 1003;
    public static final int SYSTEM_CONTROL_NOTIFICATION_ID = 1004;
    public static final int ERROR_NOTIFICATION_ID = 1005;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.ZaraApplication.Companion Companion = null;
    
    public ZaraApplication() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.hilt.work.HiltWorkerFactory getWorkerFactory() {
        return null;
    }
    
    public final void setWorkerFactory(@org.jetbrains.annotations.NotNull()
    androidx.hilt.work.HiltWorkerFactory p0) {
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    private final void initializeVoskModel() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.work.Configuration getWorkManagerConfiguration() {
        return null;
    }
    
    private final void createNotificationChannels() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/zara/assistant/ZaraApplication$Companion;", "", "()V", "AI_RESPONSE_CHANNEL_ID", "", "AI_RESPONSE_NOTIFICATION_ID", "", "ERROR_CHANNEL_ID", "ERROR_NOTIFICATION_ID", "SYSTEM_CONTROL_CHANNEL_ID", "SYSTEM_CONTROL_NOTIFICATION_ID", "VOICE_PROCESSING_CHANNEL_ID", "VOICE_PROCESSING_NOTIFICATION_ID", "WAKE_WORD_CHANNEL_ID", "WAKE_WORD_NOTIFICATION_ID", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}