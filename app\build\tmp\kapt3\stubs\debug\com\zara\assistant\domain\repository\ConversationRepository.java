package com.zara.assistant.domain.repository;

/**
 * Repository interface for conversation management
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u000f\bf\u0018\u00002\u00020\u0001J,\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\t\u0010\nJ,\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000e\u0010\u000fJ,\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u001c\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0016\u0010\u0017J\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u0017J$\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u0006\u0010\u001c\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001d\u0010\u001eJ$\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u0006\u0010 \u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b!\u0010\u001eJ$\u0010\"\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u0006\u0010\u001c\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b#\u0010\u001eJ\u001c\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010\u0017J$\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\'\u0010\u001eJ\u0010\u0010(\u001a\u0004\u0018\u00010\u0019H\u00a6@\u00a2\u0006\u0002\u0010\u0017J\u001a\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190+0*H\u00a6@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010,\u001a\u00020-2\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ\u0018\u0010.\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u001c\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ\u001c\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00060+2\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ\u000e\u00100\u001a\u000201H\u00a6@\u00a2\u0006\u0002\u0010\u0017J\u0018\u00102\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ$\u00103\u001a\b\u0012\u0004\u0012\u00020\u00190+2\u0006\u00104\u001a\u00020-2\u0006\u00105\u001a\u00020-H\u00a6@\u00a2\u0006\u0002\u00106J\u0016\u00107\u001a\u0002082\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ\"\u00109\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040+0*2\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ$\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00040+2\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010;\u001a\u000208H\u00a6@\u00a2\u0006\u0002\u0010<J$\u0010=\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u0006\u0010>\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010\u001eJ\u001c\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00190+2\u0006\u0010A\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ\u001c\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00040+2\u0006\u0010A\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001eJ,\u0010C\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010D\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bE\u0010F\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006G"}, d2 = {"Lcom/zara/assistant/domain/repository/ConversationRepository;", "", "addAIResponse", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/ConversationMessage;", "conversationId", "", "response", "Lcom/zara/assistant/domain/model/AIResponse;", "addAIResponse-0E7RQCE", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/AIResponse;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addMessage", "", "message", "addMessage-0E7RQCE", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/ConversationMessage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addUserCommand", "command", "Lcom/zara/assistant/domain/model/VoiceCommand;", "addUserCommand-0E7RQCE", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllConversations", "clearAllConversations-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createConversation", "Lcom/zara/assistant/domain/model/Conversation;", "createConversation-IoAF18A", "deleteConversation", "id", "deleteConversation-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteMessage", "messageId", "deleteMessage-gIAlu-s", "endConversation", "endConversation-gIAlu-s", "exportAllConversations", "exportAllConversations-IoAF18A", "exportConversation", "exportConversation-gIAlu-s", "getActiveConversation", "getAllConversations", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageResponseTime", "", "getConversation", "getConversationHistory", "getConversationStats", "Lcom/zara/assistant/domain/repository/ConversationStats;", "getConversationSummary", "getConversationsByDateRange", "startDate", "endDate", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMessageCount", "", "getMessages", "getRecentMessages", "limit", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "importConversations", "data", "importConversations-gIAlu-s", "searchConversations", "query", "searchMessages", "updateConversationSummary", "summary", "updateConversationSummary-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface ConversationRepository {
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getActiveConversation(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.Conversation> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversation(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.Conversation> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllConversations(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.zara.assistant.domain.model.Conversation>>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.zara.assistant.domain.model.ConversationMessage>>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationMessage>> $completion);
    
    /**
     * Conversation history
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationSummary(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion);
    
    /**
     * Search and filtering
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchConversations(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.Conversation>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationMessage>> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationsByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.Conversation>> $completion);
    
    /**
     * Statistics
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConversationStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.repository.ConversationStats> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMessageCount(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageResponseTime(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
}