// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.data.repository;

import com.zara.assistant.data.remote.api.CohereApiService;
import com.zara.assistant.data.remote.api.PerplexityApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AIRepositoryImpl_Factory implements Factory<AIRepositoryImpl> {
  private final Provider<CohereApiService> cohereApiServiceProvider;

  private final Provider<PerplexityApiService> perplexityApiServiceProvider;

  public AIRepositoryImpl_Factory(Provider<CohereApiService> cohereApiServiceProvider,
      Provider<PerplexityApiService> perplexityApiServiceProvider) {
    this.cohereApiServiceProvider = cohereApiServiceProvider;
    this.perplexityApiServiceProvider = perplexityApiServiceProvider;
  }

  @Override
  public AIRepositoryImpl get() {
    return newInstance(cohereApiServiceProvider.get(), perplexityApiServiceProvider.get());
  }

  public static AIRepositoryImpl_Factory create(Provider<CohereApiService> cohereApiServiceProvider,
      Provider<PerplexityApiService> perplexityApiServiceProvider) {
    return new AIRepositoryImpl_Factory(cohereApiServiceProvider, perplexityApiServiceProvider);
  }

  public static AIRepositoryImpl newInstance(CohereApiService cohereApiService,
      PerplexityApiService perplexityApiService) {
    return new AIRepositoryImpl(cohereApiService, perplexityApiService);
  }
}
