Gapp/src/main/java/com/zara/assistant/domain/model/UserLearningModels.kt?app/src/main/java/com/zara/assistant/domain/model/AIResponse.kt6app/src/main/java/com/zara/assistant/core/Constants.ktDapp/src/main/java/com/zara/assistant/services/AzureOnlySTTService.ktFapp/src/main/java/com/zara/assistant/services/LocalCommandProcessor.kt?app/src/main/java/com/zara/assistant/domain/model/VoiceState.ktJapp/src/main/java/com/zara/assistant/data/remote/dto/PerplexityResponse.ktBapp/src/main/java/com/zara/assistant/data/local/dao/SettingsDao.kt7app/src/main/java/com/zara/assistant/ZaraApplication.kt@app/src/main/java/com/zara/assistant/services/WakeWordService.ktQapp/src/main/java/com/zara/assistant/data/local/preferences/PreferencesManager.ktIapp/src/main/java/com/zara/assistant/domain/repository/VoiceRepository.ktHapp/src/main/java/com/zara/assistant/data/local/database/ZaraDatabase.ktWapp/src/main/java/com/zara/assistant/data/local/database/entities/ConversationEntity.ktLapp/src/main/java/com/zara/assistant/domain/repository/SettingsRepository.ktHapp/src/main/java/com/zara/assistant/data/repository/AIRepositoryImpl.ktLapp/src/main/java/com/zara/assistant/data/remote/api/PerplexityApiService.ktBapp/src/main/java/com/zara/assistant/ui/viewmodel/MainViewModel.ktGapp/src/main/java/com/zara/assistant/services/AIOrchestrationService.ktKapp/src/main/java/com/zara/assistant/presentation/utils/PerformanceUtils.kt9app/src/main/java/com/zara/assistant/di/DatabaseModule.kt=app/src/main/java/com/zara/assistant/ui/screens/MainScreen.kt;app/src/main/java/com/zara/assistant/utils/ApiKeyManager.ktFapp/src/main/java/com/zara/assistant/ui/viewmodel/SettingsViewModel.ktQapp/src/main/java/com/zara/assistant/presentation/components/NeumorphismButton.ktAapp/src/main/java/com/zara/assistant/data/local/dao/CommandDao.ktDapp/src/main/java/com/zara/assistant/ui/screens/PermissionsScreen.ktAapp/src/main/java/com/zara/assistant/domain/model/Conversation.ktNapp/src/main/java/com/zara/assistant/data/repository/SettingsRepositoryImpl.kt@app/src/main/java/com/zara/assistant/presentation/theme/Shape.ktKapp/src/main/java/com/zara/assistant/presentation/components/VoiceButton.ktPapp/src/main/java/com/zara/assistant/domain/repository/ConversationRepository.ktFapp/src/main/java/com/zara/assistant/data/local/dao/ConversationDao.ktKapp/src/main/java/com/zara/assistant/data/repository/VoiceRepositoryImpl.ktHapp/src/main/java/com/zara/assistant/data/remote/api/CohereApiService.kt8app/src/main/java/com/zara/assistant/di/NetworkModule.kt?app/src/main/java/com/zara/assistant/utils/ResponseOptimizer.ktFapp/src/main/java/com/zara/assistant/data/local/dao/UserLearningDao.ktEapp/src/main/java/com/zara/assistant/services/SystemControlManager.ktCapp/src/main/java/com/zara/assistant/ui/screens/OnboardingScreen.kt?app/src/main/java/com/zara/assistant/presentation/theme/Type.ktJapp/src/main/java/com/zara/assistant/ui/RestrictedSettingsGuideActivity.ktEapp/src/main/java/com/zara/assistant/ui/screens/CommandsHelpScreen.ktIapp/src/main/java/com/zara/assistant/services/MLPersonalizationService.ktOapp/src/main/java/com/zara/assistant/services/AdvancedVoiceProcessingService.ktEapp/src/main/java/com/zara/assistant/data/remote/dto/CohereRequest.ktOapp/src/main/java/com/zara/assistant/data/remote/interceptor/AuthInterceptor.ktDapp/src/main/java/com/zara/assistant/services/UserLearningService.ktFapp/src/main/java/com/zara/assistant/domain/repository/AIRepository.ktDapp/src/main/java/com/zara/assistant/services/AIProcessingService.kt7app/src/main/java/com/zara/assistant/ui/MainActivity.ktFapp/src/main/java/com/zara/assistant/services/PersonalMemoryService.ktLapp/src/main/java/com/zara/assistant/services/NotificationListenerService.kt4app/src/main/java/com/zara/assistant/di/AppModule.ktAapp/src/main/java/com/zara/assistant/domain/model/VoiceCommand.ktDapp/src/main/java/com/zara/assistant/ui/navigation/ZaraNavigation.ktHapp/src/main/java/com/zara/assistant/services/ZaraDeviceAdminReceiver.ktGapp/src/main/java/com/zara/assistant/domain/model/ConversationModels.ktFapp/src/main/java/com/zara/assistant/data/local/database/Converters.ktRapp/src/main/java/com/zara/assistant/data/repository/ConversationRepositoryImpl.kt>app/src/main/java/com/zara/assistant/ui/screens/AboutScreen.ktFapp/src/main/java/com/zara/assistant/data/remote/dto/CohereResponse.ktRapp/src/main/java/com/zara/assistant/presentation/components/VoiceVisualization.kt>app/src/main/java/com/zara/assistant/utils/PerformanceUtils.ktIapp/src/main/java/com/zara/assistant/services/ZaraAccessibilityService.ktAapp/src/main/java/com/zara/assistant/services/WebSearchService.kt@app/src/main/java/com/zara/assistant/presentation/theme/Color.ktIapp/src/main/java/com/zara/assistant/data/remote/dto/PerplexityRequest.ktDapp/src/main/java/com/zara/assistant/services/ConversationManager.kt8app/src/main/java/com/zara/assistant/di/ServiceModule.ktAapp/src/main/java/com/zara/assistant/ui/screens/SettingsScreen.ktQapp/src/main/java/com/zara/assistant/domain/usecase/ProcessVoiceCommandUseCase.ktAapp/src/main/java/com/zara/assistant/domain/model/SystemAction.kt@app/src/main/java/com/zara/assistant/presentation/theme/Theme.ktSapp/src/main/java/com/zara/assistant/data/local/database/entities/SettingsEntity.ktOapp/src/main/java/com/zara/assistant/presentation/components/NeumorphismCard.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 