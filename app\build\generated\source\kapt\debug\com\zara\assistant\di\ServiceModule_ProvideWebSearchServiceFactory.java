// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.data.local.dao.UserLearningDao;
import com.zara.assistant.services.WebSearchService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ServiceModule_ProvideWebSearchServiceFactory implements Factory<WebSearchService> {
  private final Provider<Context> contextProvider;

  private final Provider<UserLearningDao> userLearningDaoProvider;

  private final Provider<OkHttpClient> okHttpClientProvider;

  public ServiceModule_ProvideWebSearchServiceFactory(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider,
      Provider<OkHttpClient> okHttpClientProvider) {
    this.contextProvider = contextProvider;
    this.userLearningDaoProvider = userLearningDaoProvider;
    this.okHttpClientProvider = okHttpClientProvider;
  }

  @Override
  public WebSearchService get() {
    return provideWebSearchService(contextProvider.get(), userLearningDaoProvider.get(), okHttpClientProvider.get());
  }

  public static ServiceModule_ProvideWebSearchServiceFactory create(
      Provider<Context> contextProvider, Provider<UserLearningDao> userLearningDaoProvider,
      Provider<OkHttpClient> okHttpClientProvider) {
    return new ServiceModule_ProvideWebSearchServiceFactory(contextProvider, userLearningDaoProvider, okHttpClientProvider);
  }

  public static WebSearchService provideWebSearchService(Context context,
      UserLearningDao userLearningDao, OkHttpClient okHttpClient) {
    return Preconditions.checkNotNullFromProvides(ServiceModule.INSTANCE.provideWebSearchService(context, userLearningDao, okHttpClient));
  }
}
