// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.data.local.dao.UserLearningDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PersonalMemoryService_Factory implements Factory<PersonalMemoryService> {
  private final Provider<Context> contextProvider;

  private final Provider<UserLearningDao> userLearningDaoProvider;

  public PersonalMemoryService_Factory(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider) {
    this.contextProvider = contextProvider;
    this.userLearningDaoProvider = userLearningDaoProvider;
  }

  @Override
  public PersonalMemoryService get() {
    return newInstance(contextProvider.get(), userLearningDaoProvider.get());
  }

  public static PersonalMemoryService_Factory create(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider) {
    return new PersonalMemoryService_Factory(contextProvider, userLearningDaoProvider);
  }

  public static PersonalMemoryService newInstance(Context context,
      UserLearningDao userLearningDao) {
    return new PersonalMemoryService(context, userLearningDao);
  }
}
