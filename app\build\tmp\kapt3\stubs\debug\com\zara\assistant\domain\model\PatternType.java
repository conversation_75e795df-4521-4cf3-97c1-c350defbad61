package com.zara.assistant.domain.model;

/**
 * Pattern types for classification
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/zara/assistant/domain/model/PatternType;", "", "()V", "CONTEXT_BASED", "", "FREQUENCY_BASED", "PREFERENCE_BASED", "SEQUENCE_BASED", "TIME_BASED", "app_debug"})
public final class PatternType {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TIME_BASED = "TIME_BASED";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FREQUENCY_BASED = "FREQUENCY_BASED";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CONTEXT_BASED = "CONTEXT_BASED";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SEQUENCE_BASED = "SEQUENCE_BASED";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PREFERENCE_BASED = "PREFERENCE_BASED";
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.domain.model.PatternType INSTANCE = null;
    
    private PatternType() {
        super();
    }
}