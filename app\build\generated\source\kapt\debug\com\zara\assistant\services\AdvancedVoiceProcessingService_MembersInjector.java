// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import com.zara.assistant.domain.usecase.ProcessVoiceCommandUseCase;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdvancedVoiceProcessingService_MembersInjector implements MembersInjector<AdvancedVoiceProcessingService> {
  private final Provider<AzureOnlySTTService> azureOnlySTTServiceProvider;

  private final Provider<LocalCommandProcessor> localCommandProcessorProvider;

  private final Provider<ProcessVoiceCommandUseCase> processVoiceCommandUseCaseProvider;

  private final Provider<SystemControlManager> systemControlManagerProvider;

  private final Provider<ConversationManager> conversationManagerProvider;

  private final Provider<AIOrchestrationService> aiOrchestrationServiceProvider;

  public AdvancedVoiceProcessingService_MembersInjector(
      Provider<AzureOnlySTTService> azureOnlySTTServiceProvider,
      Provider<LocalCommandProcessor> localCommandProcessorProvider,
      Provider<ProcessVoiceCommandUseCase> processVoiceCommandUseCaseProvider,
      Provider<SystemControlManager> systemControlManagerProvider,
      Provider<ConversationManager> conversationManagerProvider,
      Provider<AIOrchestrationService> aiOrchestrationServiceProvider) {
    this.azureOnlySTTServiceProvider = azureOnlySTTServiceProvider;
    this.localCommandProcessorProvider = localCommandProcessorProvider;
    this.processVoiceCommandUseCaseProvider = processVoiceCommandUseCaseProvider;
    this.systemControlManagerProvider = systemControlManagerProvider;
    this.conversationManagerProvider = conversationManagerProvider;
    this.aiOrchestrationServiceProvider = aiOrchestrationServiceProvider;
  }

  public static MembersInjector<AdvancedVoiceProcessingService> create(
      Provider<AzureOnlySTTService> azureOnlySTTServiceProvider,
      Provider<LocalCommandProcessor> localCommandProcessorProvider,
      Provider<ProcessVoiceCommandUseCase> processVoiceCommandUseCaseProvider,
      Provider<SystemControlManager> systemControlManagerProvider,
      Provider<ConversationManager> conversationManagerProvider,
      Provider<AIOrchestrationService> aiOrchestrationServiceProvider) {
    return new AdvancedVoiceProcessingService_MembersInjector(azureOnlySTTServiceProvider, localCommandProcessorProvider, processVoiceCommandUseCaseProvider, systemControlManagerProvider, conversationManagerProvider, aiOrchestrationServiceProvider);
  }

  @Override
  public void injectMembers(AdvancedVoiceProcessingService instance) {
    injectAzureOnlySTTService(instance, azureOnlySTTServiceProvider.get());
    injectLocalCommandProcessor(instance, localCommandProcessorProvider.get());
    injectProcessVoiceCommandUseCase(instance, processVoiceCommandUseCaseProvider.get());
    injectSystemControlManager(instance, systemControlManagerProvider.get());
    injectConversationManager(instance, conversationManagerProvider.get());
    injectAiOrchestrationService(instance, aiOrchestrationServiceProvider.get());
  }

  @InjectedFieldSignature("com.zara.assistant.services.AdvancedVoiceProcessingService.azureOnlySTTService")
  public static void injectAzureOnlySTTService(AdvancedVoiceProcessingService instance,
      AzureOnlySTTService azureOnlySTTService) {
    instance.azureOnlySTTService = azureOnlySTTService;
  }

  @InjectedFieldSignature("com.zara.assistant.services.AdvancedVoiceProcessingService.localCommandProcessor")
  public static void injectLocalCommandProcessor(AdvancedVoiceProcessingService instance,
      LocalCommandProcessor localCommandProcessor) {
    instance.localCommandProcessor = localCommandProcessor;
  }

  @InjectedFieldSignature("com.zara.assistant.services.AdvancedVoiceProcessingService.processVoiceCommandUseCase")
  public static void injectProcessVoiceCommandUseCase(AdvancedVoiceProcessingService instance,
      ProcessVoiceCommandUseCase processVoiceCommandUseCase) {
    instance.processVoiceCommandUseCase = processVoiceCommandUseCase;
  }

  @InjectedFieldSignature("com.zara.assistant.services.AdvancedVoiceProcessingService.systemControlManager")
  public static void injectSystemControlManager(AdvancedVoiceProcessingService instance,
      SystemControlManager systemControlManager) {
    instance.systemControlManager = systemControlManager;
  }

  @InjectedFieldSignature("com.zara.assistant.services.AdvancedVoiceProcessingService.conversationManager")
  public static void injectConversationManager(AdvancedVoiceProcessingService instance,
      ConversationManager conversationManager) {
    instance.conversationManager = conversationManager;
  }

  @InjectedFieldSignature("com.zara.assistant.services.AdvancedVoiceProcessingService.aiOrchestrationService")
  public static void injectAiOrchestrationService(AdvancedVoiceProcessingService instance,
      AIOrchestrationService aiOrchestrationService) {
    instance.aiOrchestrationService = aiOrchestrationService;
  }
}
