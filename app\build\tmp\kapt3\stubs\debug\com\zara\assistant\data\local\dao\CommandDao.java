package com.zara.assistant.data.local.dao;

/**
 * DAO for voice command operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\nH\'J\u0018\u0010\r\u001a\u0004\u0018\u00010\f2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0010\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\u0012\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\u0018\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u0019"}, d2 = {"Lcom/zara/assistant/data/local/dao/CommandDao;", "", "deleteAllCommands", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCommandById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllCommands", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/zara/assistant/data/local/database/entities/VoiceCommandEntity;", "getCommandById", "getFailedCommandCount", "", "getProcessedCommandCount", "getRecentCommands", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertCommand", "command", "(Lcom/zara/assistant/data/local/database/entities/VoiceCommandEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchCommands", "query", "app_debug"})
@androidx.room.Dao()
public abstract interface CommandDao {
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.zara.assistant.data.local.database.entities.VoiceCommandEntity>> getAllCommands();
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCommandById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.data.local.database.entities.VoiceCommandEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands ORDER BY timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentCommands(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.database.entities.VoiceCommandEntity>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertCommand(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.database.entities.VoiceCommandEntity command, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM voice_commands WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCommandById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM voice_commands")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllCommands(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE text LIKE \'%\' || :query || \'%\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchCommands(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.data.local.database.entities.VoiceCommandEntity>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM voice_commands WHERE isProcessed = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProcessedCommandCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM voice_commands WHERE isProcessed = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFailedCommandCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}