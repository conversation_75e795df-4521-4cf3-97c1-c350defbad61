// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.services;

import android.content.Context;
import com.zara.assistant.data.local.dao.UserLearningDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WebSearchService_Factory implements Factory<WebSearchService> {
  private final Provider<Context> contextProvider;

  private final Provider<UserLearningDao> userLearningDaoProvider;

  private final Provider<OkHttpClient> okHttpClientProvider;

  public WebSearchService_Factory(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider,
      Provider<OkHttpClient> okHttpClientProvider) {
    this.contextProvider = contextProvider;
    this.userLearningDaoProvider = userLearningDaoProvider;
    this.okHttpClientProvider = okHttpClientProvider;
  }

  @Override
  public WebSearchService get() {
    return newInstance(contextProvider.get(), userLearningDaoProvider.get(), okHttpClientProvider.get());
  }

  public static WebSearchService_Factory create(Provider<Context> contextProvider,
      Provider<UserLearningDao> userLearningDaoProvider,
      Provider<OkHttpClient> okHttpClientProvider) {
    return new WebSearchService_Factory(contextProvider, userLearningDaoProvider, okHttpClientProvider);
  }

  public static WebSearchService newInstance(Context context, UserLearningDao userLearningDao,
      OkHttpClient okHttpClient) {
    return new WebSearchService(context, userLearningDao, okHttpClient);
  }
}
