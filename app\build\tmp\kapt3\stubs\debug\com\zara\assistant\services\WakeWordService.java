package com.zara.assistant.services;

/**
 * Foreground service for wake word detection using Picovoice Porcupine
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\f\n\u0002\u0010\u0007\n\u0002\b\u0003\b\u0007\u0018\u0000 92\u00020\u0001:\u00029:B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001d\u001a\u00020\u0005H\u0002J\b\u0010\u001e\u001a\u00020\u001fH\u0002J\b\u0010 \u001a\u00020!H\u0002J\b\u0010\"\u001a\u00020!H\u0002J\u0006\u0010#\u001a\u00020\u0005J\u0012\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\'H\u0016J\b\u0010(\u001a\u00020!H\u0016J\b\u0010)\u001a\u00020!H\u0016J\"\u0010*\u001a\u00020+2\b\u0010&\u001a\u0004\u0018\u00010\'2\u0006\u0010,\u001a\u00020+2\u0006\u0010-\u001a\u00020+H\u0016J\u0012\u0010.\u001a\u00020!2\b\u0010/\u001a\u0004\u0018\u00010\'H\u0016J\u0010\u00100\u001a\u00020!2\u0006\u00101\u001a\u00020+H\u0002J\b\u00102\u001a\u00020!H\u0002J\b\u00103\u001a\u00020!H\u0002J\b\u00104\u001a\u00020!H\u0002J\b\u00105\u001a\u00020!H\u0002J\u000e\u00106\u001a\u00020!2\u0006\u00107\u001a\u000208R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\b\u001a\u00060\tR\u00020\u0000X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u000f\u001a\u00020\u00108\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\r\u00a8\u0006;"}, d2 = {"Lcom/zara/assistant/services/WakeWordService;", "Landroid/app/Service;", "()V", "_detectionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_wakeWordDetected", "", "binder", "Lcom/zara/assistant/services/WakeWordService$WakeWordBinder;", "detectionState", "Lkotlinx/coroutines/flow/StateFlow;", "getDetectionState", "()Lkotlinx/coroutines/flow/StateFlow;", "isDetectionActive", "notificationManager", "Landroid/app/NotificationManager;", "getNotificationManager", "()Landroid/app/NotificationManager;", "setNotificationManager", "(Landroid/app/NotificationManager;)V", "porcupineManager", "Lai/picovoice/porcupine/PorcupineManager;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "textToSpeech", "Landroid/speech/tts/TextToSpeech;", "wakeWordDetected", "getWakeWordDetected", "checkMicrophonePermission", "createNotification", "Landroid/app/Notification;", "initializePorcupine", "", "initializeTTS", "isActive", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "onDestroy", "onStartCommand", "", "flags", "startId", "onTaskRemoved", "rootIntent", "onWakeWordDetected", "keywordIndex", "speakAcknowledgmentAndStartSTT", "startVoiceProcessingAfterAcknowledgment", "startWakeWordDetection", "stopWakeWordDetection", "updateSensitivity", "sensitivity", "", "Companion", "WakeWordBinder", "app_debug"})
public final class WakeWordService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "WakeWordService";
    private static final int NOTIFICATION_ID = 1001;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_DETECTION = "START_DETECTION";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_DETECTION = "STOP_DETECTION";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_WAKE_WORD_DETECTED = "WAKE_WORD_DETECTED";
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.services.WakeWordService.WakeWordBinder binder = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.Nullable()
    private ai.picovoice.porcupine.PorcupineManager porcupineManager;
    private boolean isDetectionActive = false;
    @org.jetbrains.annotations.Nullable()
    private android.speech.tts.TextToSpeech textToSpeech;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _detectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> detectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Long> _wakeWordDetected = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> wakeWordDetected = null;
    @javax.inject.Inject()
    public android.app.NotificationManager notificationManager;
    @org.jetbrains.annotations.NotNull()
    public static final com.zara.assistant.services.WakeWordService.Companion Companion = null;
    
    public WakeWordService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getDetectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getWakeWordDetected() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.app.NotificationManager getNotificationManager() {
        return null;
    }
    
    public final void setNotificationManager(@org.jetbrains.annotations.NotNull()
    android.app.NotificationManager p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    private final void initializeTTS() {
    }
    
    private final void speakAcknowledgmentAndStartSTT() {
    }
    
    private final void startVoiceProcessingAfterAcknowledgment() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    public void onTaskRemoved(@org.jetbrains.annotations.Nullable()
    android.content.Intent rootIntent) {
    }
    
    private final void startWakeWordDetection() {
    }
    
    private final void stopWakeWordDetection() {
    }
    
    private final void initializePorcupine() {
    }
    
    private final void onWakeWordDetected(int keywordIndex) {
    }
    
    private final android.app.Notification createNotification() {
        return null;
    }
    
    private final boolean checkMicrophonePermission() {
        return false;
    }
    
    public final void updateSensitivity(float sensitivity) {
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/zara/assistant/services/WakeWordService$Companion;", "", "()V", "ACTION_START_DETECTION", "", "ACTION_STOP_DETECTION", "ACTION_WAKE_WORD_DETECTED", "NOTIFICATION_ID", "", "TAG", "startService", "", "context", "Landroid/content/Context;", "stopService", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void startService(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
        
        public final void stopService(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/zara/assistant/services/WakeWordService$WakeWordBinder;", "Landroid/os/Binder;", "(Lcom/zara/assistant/services/WakeWordService;)V", "getService", "Lcom/zara/assistant/services/WakeWordService;", "app_debug"})
    public final class WakeWordBinder extends android.os.Binder {
        
        public WakeWordBinder() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.zara.assistant.services.WakeWordService getService() {
            return null;
        }
    }
}