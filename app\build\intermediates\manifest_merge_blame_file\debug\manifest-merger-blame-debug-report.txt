1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.zara.assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Audio and Speech Permissions -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:7:22-77
14
15    <!-- Network Permissions -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:5-67
16-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- System Control Permissions -->
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:5-78
20-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:14:22-75
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:5-68
21-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:15:22-65
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:5-77
22-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
23-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:17:5-88
23-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:17:22-85
24
25    <!-- WiFi Control Permissions -->
26    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:5-76
26-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:20:22-73
27    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
27-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:21:5-76
27-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:21:22-73
28
29    <!-- Bluetooth Control Permissions -->
30    <uses-permission android:name="android.permission.BLUETOOTH" />
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:5-68
30-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:24:22-65
31    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:5-74
31-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:25:22-71
32    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:5-76
32-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:26:22-73
33    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:5-73
33-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:27:22-70
34    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
34-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:28:5-78
34-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:28:22-75
35    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
35-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:29:5-30:47
35-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:29:22-76
36
37    <!-- Mobile Data and Network Control -->
38    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:5-79
38-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:33:22-76
39    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:5-35:47
39-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:34:22-77
40
41    <!-- Hotspot Control -->
42    <uses-permission android:name="android.permission.TETHER_PRIVILEGED" />
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:38:5-39:47
42-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:38:22-73
43
44    <!-- NFC Control -->
45    <uses-permission android:name="android.permission.NFC" />
45-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:5-62
45-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:42:22-59
46    <uses-permission android:name="android.permission.NFC_PREFERRED_PAYMENT_INFO" />
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:5-85
46-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:43:22-82
47
48    <!-- Location Services Control -->
49    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:5-79
49-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:46:22-76
50    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
50-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:5-81
50-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:47:22-78
51    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
51-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:5-85
51-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:48:22-82
52
53    <!-- Device Control Permissions -->
54    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:5-76
54-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:51:22-73
55    <uses-permission android:name="android.permission.REORDER_TASKS" />
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:52:5-72
55-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:52:22-69
56    <uses-permission android:name="android.permission.GET_TASKS" />
56-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:53:5-68
56-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:53:22-65
57    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
57-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:5-84
57-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:54:22-81
58    <uses-permission android:name="android.permission.SET_WALLPAPER" />
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:5-72
58-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:55:22-69
59    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:56:5-78
59-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:56:22-75
60    <uses-permission android:name="android.permission.DEVICE_POWER" />
60-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:5-58:47
60-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:22-68
61    <uses-permission android:name="android.permission.REBOOT" />
61-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:5-60:47
61-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:59:22-62
62
63    <!-- Screen and Display Control -->
64    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
64-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:5-79
64-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:63:22-76
65    <uses-permission android:name="android.permission.FLASHLIGHT" />
65-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:5-69
65-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:64:22-66
66
67    <!-- Telephony Control -->
68    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
68-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:5-75
68-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:67:22-72
69    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
69-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:5-77
69-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:68:22-74
70    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
70-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:5-70:47
70-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:69:22-74
71
72    <!-- App Management Permissions -->
73    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
73-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:73:5-77
73-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:73:22-74
74    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
74-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:5-75:47
74-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:74:22-75
75
76    <!-- System Settings Access -->
77    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
77-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:5-73
77-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:78:22-70
78    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
78-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:5-80:47
78-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:22-77
79    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
79-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:5-85
79-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:81:22-82
80
81    <!-- Notification Permissions -->
82    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
82-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:5-93
82-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:84:22-90
83    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
83-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:5-77
83-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:85:22-74
84
85    <!-- Accessibility Permission -->
86    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
86-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:88:5-85
86-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:88:22-82
87
88    <!-- Phone and Device Control -->
89    <uses-permission android:name="android.permission.CALL_PHONE" />
89-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:5-69
89-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:91:22-66
90    <uses-permission android:name="android.permission.SEND_SMS" />
90-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:5-67
90-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:92:22-64
91    <uses-permission android:name="android.permission.READ_CONTACTS" />
91-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:5-72
91-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:93:22-69
92
93    <!-- Storage Permissions -->
94    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
94-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:5-80
94-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:96:22-77
95    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
95-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:5-81
95-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:97:22-78
96
97    <!-- Advanced System Control -->
98    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
98-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:5-101:47
98-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:100:22-77
99    <uses-permission android:name="android.permission.MANAGE_USERS" />
99-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:102:5-103:47
99-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:102:22-68
100    <uses-permission android:name="android.permission.STATUS_BAR" />
100-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:104:5-105:47
100-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:104:22-66
101
102    <!-- Root-level System Access (for advanced features) -->
103    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
103-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:5-109:47
103-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:108:22-72
104    <uses-permission android:name="android.permission.HARDWARE_TEST" />
104-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:5-111:47
104-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:110:22-69
105
106    <!-- Device Admin Permissions -->
107    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
107-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:5-76
107-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:114:22-73
108    <uses-permission android:name="android.permission.DEVICE_POWER" />
108-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:5-58:47
108-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:57:22-68
109    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
109-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:5-80:47
109-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:79:22-77
110    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
111
112    <permission
112-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
113        android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
113-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
114        android:protectionLevel="signature" />
114-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
115
116    <uses-permission android:name="com.zara.assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
116-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
116-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
117
118    <application
118-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:120:5-232:19
119        android:name="com.zara.assistant.ZaraApplication"
119-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:121:9-40
120        android:allowBackup="true"
120-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:122:9-35
121        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
121-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
122        android:dataExtractionRules="@xml/data_extraction_rules"
122-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:123:9-65
123        android:debuggable="true"
124        android:extractNativeLibs="false"
125        android:fullBackupContent="@xml/backup_rules"
125-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:124:9-54
126        android:icon="@mipmap/ic_launcher"
126-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:125:9-43
127        android:label="@string/app_name"
127-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:126:9-41
128        android:roundIcon="@mipmap/ic_launcher_round"
128-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:127:9-54
129        android:supportsRtl="true"
129-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:128:9-35
130        android:theme="@style/Theme.Zara"
130-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:129:9-42
131        android:usesCleartextTraffic="false" >
131-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:130:9-45
132
133        <!-- Main Activity -->
134        <activity
134-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:134:9-148:20
135            android:name="com.zara.assistant.ui.MainActivity"
135-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:135:13-44
136            android:exported="true"
136-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:136:13-36
137            android:launchMode="singleTop"
137-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:139:13-43
138            android:screenOrientation="portrait"
138-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:138:13-49
139            android:theme="@style/Theme.Zara" >
139-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:137:13-46
140            <intent-filter>
140-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:140:13-143:29
141                <action android:name="android.intent.action.MAIN" />
141-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:141:17-69
141-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:141:25-66
142
143                <category android:name="android.intent.category.LAUNCHER" />
143-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:142:17-77
143-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:142:27-74
144            </intent-filter>
145            <intent-filter>
145-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:144:13-147:29
146                <action android:name="android.intent.action.VOICE_COMMAND" />
146-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:17-78
146-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:145:25-75
147
148                <category android:name="android.intent.category.DEFAULT" />
148-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:17-76
148-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:146:27-73
149            </intent-filter>
150        </activity>
151
152        <!-- Restricted Settings Guide Activity -->
153        <activity
153-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:151:9-155:52
154            android:name="com.zara.assistant.ui.RestrictedSettingsGuideActivity"
154-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:152:13-63
155            android:exported="false"
155-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:153:13-37
156            android:screenOrientation="portrait"
156-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:155:13-49
157            android:theme="@style/Theme.Zara" />
157-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:154:13-46
158
159        <!-- Wake Word Detection Service -->
160        <service
160-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:158:9-162:58
161            android:name="com.zara.assistant.services.WakeWordService"
161-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:159:13-53
162            android:enabled="true"
162-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:160:13-35
163            android:exported="false"
163-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:161:13-37
164            android:foregroundServiceType="microphone" />
164-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:162:13-55
165
166        <!-- Advanced Voice Processing Service -->
167        <service
167-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:165:9-169:58
168            android:name="com.zara.assistant.services.AdvancedVoiceProcessingService"
168-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:166:13-68
169            android:enabled="true"
169-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:167:13-35
170            android:exported="false"
170-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:168:13-37
171            android:foregroundServiceType="microphone" />
171-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:169:13-55
172
173        <!-- Accessibility Service for System Control -->
174        <service
174-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:172:9-182:19
175            android:name="com.zara.assistant.services.ZaraAccessibilityService"
175-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:173:13-62
176            android:exported="true"
176-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:175:13-36
177            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
177-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:174:13-79
178            <intent-filter>
178-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:176:13-178:29
179                <action android:name="android.accessibilityservice.AccessibilityService" />
179-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:177:17-92
179-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:177:25-89
180            </intent-filter>
181
182            <meta-data
182-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:179:13-181:72
183                android:name="android.accessibilityservice"
183-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:180:17-60
184                android:resource="@xml/accessibility_service_config" />
184-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:181:17-69
185        </service>
186
187        <!-- Device Admin Receiver for Enhanced System Control -->
188        <receiver
188-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:185:9-196:20
189            android:name="com.zara.assistant.services.ZaraDeviceAdminReceiver"
189-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:186:13-61
190            android:exported="true"
190-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:188:13-36
191            android:permission="android.permission.BIND_DEVICE_ADMIN" >
191-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:187:13-70
192            <meta-data
192-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:189:13-191:63
193                android:name="android.app.device_admin"
193-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:190:17-56
194                android:resource="@xml/device_admin_config" />
194-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:191:17-60
195
196            <intent-filter>
196-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:192:13-195:29
197                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
197-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:193:17-82
197-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:193:25-79
198                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
198-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:194:17-83
198-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:194:25-80
199            </intent-filter>
200        </receiver>
201
202        <!-- Notification Listener Service -->
203        <service
203-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:199:9-206:19
204            android:name="com.zara.assistant.services.NotificationListenerService"
204-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:200:13-65
205            android:exported="true"
205-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:202:13-36
206            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
206-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:201:13-87
207            <intent-filter>
207-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:203:13-205:29
208                <action android:name="android.service.notification.NotificationListenerService" />
208-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:204:17-99
208-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:204:25-96
209            </intent-filter>
210        </service>
211
212        <!-- AI Processing Service -->
213        <service
213-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:209:9-212:40
214            android:name="com.zara.assistant.services.AIProcessingService"
214-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:210:13-57
215            android:enabled="true"
215-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:211:13-35
216            android:exported="false" />
216-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:212:13-37
217
218        <!-- User Learning Service -->
219        <service
219-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:215:9-218:40
220            android:name="com.zara.assistant.services.UserLearningService"
220-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:216:13-57
221            android:enabled="true"
221-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:217:13-35
222            android:exported="false" />
222-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:218:13-37
223
224        <!-- Work Manager for Background Tasks -->
225        <provider
226            android:name="androidx.startup.InitializationProvider"
226-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:222:13-67
227            android:authorities="com.zara.assistant.androidx-startup"
227-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:223:13-68
228            android:exported="false" >
228-->C:\Users\<USER>\Desktop\Zara\Zara\app\src\main\AndroidManifest.xml:224:13-37
229            <meta-data
229-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
230                android:name="androidx.emoji2.text.EmojiCompatInitializer"
230-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
231                android:value="androidx.startup" />
231-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
232            <meta-data
232-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
233-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
234                android:value="androidx.startup" />
234-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
235            <meta-data
235-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
236                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
237                android:value="androidx.startup" />
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
238        </provider>
239
240        <activity
240-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
241            android:name="androidx.activity.ComponentActivity"
241-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
242            android:exported="true" />
242-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
243        <activity
243-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
244            android:name="androidx.compose.ui.tooling.PreviewActivity"
244-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
245            android:exported="true" />
245-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
246
247        <service
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
248            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
250            android:enabled="@bool/enable_system_alarm_service_default"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
251            android:exported="false" />
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
252        <service
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
253            android:name="androidx.work.impl.background.systemjob.SystemJobService"
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
254            android:directBootAware="false"
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
255            android:enabled="@bool/enable_system_job_service_default"
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
256            android:exported="true"
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
257            android:permission="android.permission.BIND_JOB_SERVICE" />
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
258        <service
258-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
259            android:name="androidx.work.impl.foreground.SystemForegroundService"
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
261            android:enabled="@bool/enable_system_foreground_service_default"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
262            android:exported="false" />
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
263
264        <receiver
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
265            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
267            android:enabled="true"
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
268            android:exported="false" />
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
269        <receiver
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
275                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
276                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
277            </intent-filter>
278        </receiver>
279        <receiver
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
280            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
285                <action android:name="android.intent.action.BATTERY_OKAY" />
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
286                <action android:name="android.intent.action.BATTERY_LOW" />
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
295                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
296                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
300            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
302            android:enabled="false"
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
303            android:exported="false" >
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
304            <intent-filter>
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
305                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
306            </intent-filter>
307        </receiver>
308        <receiver
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
309            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
310            android:directBootAware="false"
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
311            android:enabled="false"
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
312            android:exported="false" >
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
313            <intent-filter>
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
314                <action android:name="android.intent.action.BOOT_COMPLETED" />
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
315                <action android:name="android.intent.action.TIME_SET" />
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
316                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
320            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
320-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
322            android:enabled="@bool/enable_system_alarm_service_default"
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
323            android:exported="false" >
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
324            <intent-filter>
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
325                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
326            </intent-filter>
327        </receiver>
328        <receiver
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
329            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
330            android:directBootAware="false"
330-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
331            android:enabled="true"
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
332            android:exported="true"
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
333            android:permission="android.permission.DUMP" >
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
334            <intent-filter>
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
335                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
336            </intent-filter>
337        </receiver>
338
339        <service
339-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
340            android:name="androidx.room.MultiInstanceInvalidationService"
340-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
341            android:directBootAware="true"
341-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
342            android:exported="false" />
342-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
343
344        <receiver
344-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
345            android:name="androidx.profileinstaller.ProfileInstallReceiver"
345-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
346            android:directBootAware="false"
346-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
347            android:enabled="true"
347-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
348            android:exported="true"
348-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
349            android:permission="android.permission.DUMP" >
349-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
350            <intent-filter>
350-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
351                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
351-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
351-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
352            </intent-filter>
353            <intent-filter>
353-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
354                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
354-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
354-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
355            </intent-filter>
356            <intent-filter>
356-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
357                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
357-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
357-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
358            </intent-filter>
359            <intent-filter>
359-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
360                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\70825ede73ee2de476a5c0969d371437\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
361            </intent-filter>
362        </receiver>
363
364        <provider
364-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:15:9-18:40
365            android:name="com.microsoft.cognitiveservices.speech.util.InternalContentProvider"
365-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:17:13-57
366            android:authorities="com.zara.assistant.MicrosoftCognitiveServicesSpeech.InternalContentProvider"
366-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:16:13-108
367            android:exported="false" />
367-->[com.microsoft.cognitiveservices.speech:client-sdk:1.43.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4974087f5829ddbc892a39bfab17804\transformed\jetified-client-sdk-1.43.0\AndroidManifest.xml:18:13-37
368    </application>
369
370</manifest>
