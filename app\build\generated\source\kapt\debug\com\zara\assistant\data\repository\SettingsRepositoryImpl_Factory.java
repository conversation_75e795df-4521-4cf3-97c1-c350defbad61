// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.data.repository;

import com.zara.assistant.data.local.preferences.PreferencesManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsRepositoryImpl_Factory implements Factory<SettingsRepositoryImpl> {
  private final Provider<PreferencesManager> preferencesManagerProvider;

  public SettingsRepositoryImpl_Factory(Provider<PreferencesManager> preferencesManagerProvider) {
    this.preferencesManagerProvider = preferencesManagerProvider;
  }

  @Override
  public SettingsRepositoryImpl get() {
    return newInstance(preferencesManagerProvider.get());
  }

  public static SettingsRepositoryImpl_Factory create(
      Provider<PreferencesManager> preferencesManagerProvider) {
    return new SettingsRepositoryImpl_Factory(preferencesManagerProvider);
  }

  public static SettingsRepositoryImpl newInstance(PreferencesManager preferencesManager) {
    return new SettingsRepositoryImpl(preferencesManager);
  }
}
