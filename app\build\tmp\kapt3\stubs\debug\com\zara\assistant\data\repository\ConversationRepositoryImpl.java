package com.zara.assistant.data.repository;

/**
 * Implementation of ConversationRepository
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J,\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000e\u0010\u000fJ,\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0012\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014J,\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\u0017H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0018\u0010\u0019J\u001c\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00110\bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001b\u0010\u001cJ\u001c\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001e0\bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001f\u0010\u001cJ$\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00110\b2\u0006\u0010!\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J$\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00110\b2\u0006\u0010%\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b&\u0010#J$\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00110\b2\u0006\u0010!\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b(\u0010#J\u001c\u0010)\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b*\u0010\u001cJ$\u0010+\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b,\u0010#J\u0010\u0010-\u001a\u0004\u0018\u00010\u001eH\u0096@\u00a2\u0006\u0002\u0010\u001cJ\u001a\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e000/H\u0096@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u00101\u001a\u0002022\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J\u0018\u00103\u001a\u0004\u0018\u00010\u001e2\u0006\u0010!\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J\u001c\u00104\u001a\b\u0012\u0004\u0012\u00020\u000b002\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J\u000e\u00105\u001a\u000206H\u0096@\u00a2\u0006\u0002\u0010\u001cJ\u0018\u00107\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J$\u00108\u001a\b\u0012\u0004\u0012\u00020\u001e002\u0006\u00109\u001a\u0002022\u0006\u0010:\u001a\u000202H\u0096@\u00a2\u0006\u0002\u0010;J\u0016\u0010<\u001a\u00020=2\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J\"\u0010>\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t000/2\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J$\u0010?\u001a\b\u0012\u0004\u0012\u00020\t002\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010@\u001a\u00020=H\u0096@\u00a2\u0006\u0002\u0010AJ$\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00110\b2\u0006\u0010C\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bD\u0010#J\u001c\u0010E\u001a\b\u0012\u0004\u0012\u00020\u001e002\u0006\u0010F\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J\u001c\u0010G\u001a\b\u0012\u0004\u0012\u00020\t002\u0006\u0010F\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010#J,\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00110\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010I\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bJ\u0010KJ\f\u0010L\u001a\u00020\u001e*\u00020MH\u0002J\f\u0010L\u001a\u00020\t*\u00020NH\u0002J\f\u0010O\u001a\u00020M*\u00020\u001eH\u0002J\f\u0010O\u001a\u00020N*\u00020\tH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006P"}, d2 = {"Lcom/zara/assistant/data/repository/ConversationRepositoryImpl;", "Lcom/zara/assistant/domain/repository/ConversationRepository;", "conversationDao", "Lcom/zara/assistant/data/local/dao/ConversationDao;", "gson", "Lcom/google/gson/Gson;", "(Lcom/zara/assistant/data/local/dao/ConversationDao;Lcom/google/gson/Gson;)V", "addAIResponse", "Lkotlin/Result;", "Lcom/zara/assistant/domain/model/ConversationMessage;", "conversationId", "", "response", "Lcom/zara/assistant/domain/model/AIResponse;", "addAIResponse-0E7RQCE", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/AIResponse;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addMessage", "", "message", "addMessage-0E7RQCE", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/ConversationMessage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addUserCommand", "command", "Lcom/zara/assistant/domain/model/VoiceCommand;", "addUserCommand-0E7RQCE", "(Ljava/lang/String;Lcom/zara/assistant/domain/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllConversations", "clearAllConversations-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createConversation", "Lcom/zara/assistant/domain/model/Conversation;", "createConversation-IoAF18A", "deleteConversation", "id", "deleteConversation-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteMessage", "messageId", "deleteMessage-gIAlu-s", "endConversation", "endConversation-gIAlu-s", "exportAllConversations", "exportAllConversations-IoAF18A", "exportConversation", "exportConversation-gIAlu-s", "getActiveConversation", "getAllConversations", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageResponseTime", "", "getConversation", "getConversationHistory", "getConversationStats", "Lcom/zara/assistant/domain/repository/ConversationStats;", "getConversationSummary", "getConversationsByDateRange", "startDate", "endDate", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMessageCount", "", "getMessages", "getRecentMessages", "limit", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "importConversations", "data", "importConversations-gIAlu-s", "searchConversations", "query", "searchMessages", "updateConversationSummary", "summary", "updateConversationSummary-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "toDomain", "Lcom/zara/assistant/data/local/database/entities/ConversationEntity;", "Lcom/zara/assistant/data/local/database/entities/ConversationMessageEntity;", "toEntity", "app_debug"})
public final class ConversationRepositoryImpl implements com.zara.assistant.domain.repository.ConversationRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.data.local.dao.ConversationDao conversationDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    
    @javax.inject.Inject()
    public ConversationRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.data.local.dao.ConversationDao conversationDao, @org.jetbrains.annotations.NotNull()
    com.google.gson.Gson gson) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getActiveConversation(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.Conversation> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getConversation(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.model.Conversation> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAllConversations(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.zara.assistant.domain.model.Conversation>>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.zara.assistant.domain.model.ConversationMessage>>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getRecentMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationMessage>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getConversationHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getConversationSummary(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object searchConversations(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.Conversation>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object searchMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.ConversationMessage>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getConversationsByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.zara.assistant.domain.model.Conversation>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getConversationStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.zara.assistant.domain.repository.ConversationStats> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getMessageCount(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAverageResponseTime(@org.jetbrains.annotations.NotNull()
    java.lang.String conversationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    private final com.zara.assistant.data.local.database.entities.ConversationEntity toEntity(com.zara.assistant.domain.model.Conversation $this$toEntity) {
        return null;
    }
    
    private final com.zara.assistant.domain.model.Conversation toDomain(com.zara.assistant.data.local.database.entities.ConversationEntity $this$toDomain) {
        return null;
    }
    
    private final com.zara.assistant.data.local.database.entities.ConversationMessageEntity toEntity(com.zara.assistant.domain.model.ConversationMessage $this$toEntity) {
        return null;
    }
    
    private final com.zara.assistant.domain.model.ConversationMessage toDomain(com.zara.assistant.data.local.database.entities.ConversationMessageEntity $this$toDomain) {
        return null;
    }
}