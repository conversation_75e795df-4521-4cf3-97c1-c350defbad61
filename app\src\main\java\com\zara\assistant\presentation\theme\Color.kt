package com.zara.assistant.presentation.theme

import androidx.compose.ui.graphics.Color

// Enhanced Primary Brand Colors
val DeepTeal = Color(0xFF006B5C)
val SoftCoral = Color(0xFFFF6B6B)
val LuminousBlue = Color(0xFF4FC3F7)
val ElectricPurple = Color(0xFF8B5CF6)
val GoldenAmber = Color(0xFFFBBF24)
val EmeraldGreen = Color(0xFF10B981)

// Extended Brand Palette
val DeepTealVariant = Color(0xFF004D40)
val SoftCoralVariant = Color(0xFFFF5252)
val LuminousBlueVariant = Color(0xFF29B6F6)
val ElectricPurpleVariant = Color(0xFF7C3AED)

// Modern Neumorphism Base Colors
val NeuroBackgroundLight = Color(0xFFF0F2F5)
val NeuroBackgroundDark = Color(0xFF1A1B23)
val NeuroBackgroundMedium = Color(0xFF2D2E36)

// Enhanced Light Theme Neumorphism
val NeuroLightShadow = Color(0xFFBEC8D1)
val NeuroLightHighlight = Color(0xFFFFFFFF)
val NeuroSurfaceLight = Color(0xFFF0F2F5)
val NeuroSurfaceLightElevated = Color(0xFFFFFFFF)

// Enhanced Dark Theme Neumorphism
val NeuroDarkShadow = Color(0xFF0F1015)
val NeuroDarkHighlight = Color(0xFF3A3B42)
val NeuroSurfaceDark = Color(0xFF1A1B23)
val NeuroSurfaceDarkElevated = Color(0xFF2D2E36)

// Modern Text Colors
val TextPrimaryLight = Color(0xFF1A1B23)
val TextSecondaryLight = Color(0xFF6B7280)
val TextTertiaryLight = Color(0xFF9CA3AF)
val TextDisabledLight = Color(0xFFD1D5DB)

val TextPrimaryDark = Color(0xFFF9FAFB)
val TextSecondaryDark = Color(0xFFD1D5DB)
val TextTertiaryDark = Color(0xFF9CA3AF)
val TextDisabledDark = Color(0xFF6B7280)

// Enhanced Accent Colors
val AccentSuccess = Color(0xFF10B981)
val AccentSuccessLight = Color(0xFF34D399)
val AccentWarning = Color(0xFFF59E0B)
val AccentWarningLight = Color(0xFFFBBF24)
val AccentError = Color(0xFFEF4444)
val AccentErrorLight = Color(0xFFF87171)
val AccentInfo = Color(0xFF3B82F6)
val AccentInfoLight = Color(0xFF60A5FA)

// Enhanced Voice Interaction Colors
val VoiceListening = Color(0xFF4FC3F7)
val VoiceListeningGlow = Color(0x334FC3F7)
val VoiceProcessing = Color(0xFF8B5CF6)
val VoiceProcessingGlow = Color(0x338B5CF6)
val VoiceSpeaking = Color(0xFF10B981)
val VoiceSpeakingGlow = Color(0x3310B981)
val VoiceIdle = Color(0xFF6B7280)
val VoiceError = Color(0xFFEF4444)

// Modern Gradient Colors
val GradientPrimary = listOf(Color(0xFF4FC3F7), Color(0xFF006B5C))
val GradientSecondary = listOf(Color(0xFF8B5CF6), Color(0xFFFF6B6B))
val GradientAccent = listOf(Color(0xFF10B981), Color(0xFFFBBF24))
val GradientNeutral = listOf(Color(0xFF6B7280), Color(0xFF374151))

// Material Design 3 Light Colors
val md_theme_light_primary = Color(0xFF006B5C)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFF7FF8E6)
val md_theme_light_onPrimaryContainer = Color(0xFF00201B)
val md_theme_light_secondary = Color(0xFF4A635F)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFCCE8E2)
val md_theme_light_onSecondaryContainer = Color(0xFF05201C)
val md_theme_light_tertiary = Color(0xFF456179)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFCCE7FF)
val md_theme_light_onTertiaryContainer = Color(0xFF001E31)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFF5FFF9)
val md_theme_light_onBackground = Color(0xFF161D1A)
val md_theme_light_surface = Color(0xFFF5FFF9)
val md_theme_light_onSurface = Color(0xFF161D1A)
val md_theme_light_surfaceVariant = Color(0xFFDAE5E1)
val md_theme_light_onSurfaceVariant = Color(0xFF3F4946)
val md_theme_light_outline = Color(0xFF6F7976)
val md_theme_light_inverseOnSurface = Color(0xFFECF2EE)
val md_theme_light_inverseSurface = Color(0xFF2B322F)
val md_theme_light_inversePrimary = Color(0xFF63DBCA)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = Color(0xFF006B5C)
val md_theme_light_outlineVariant = Color(0xFFBEC9C5)
val md_theme_light_scrim = Color(0xFF000000)

// Material Design 3 Dark Colors
val md_theme_dark_primary = Color(0xFF63DBCA)
val md_theme_dark_onPrimary = Color(0xFF003730)
val md_theme_dark_primaryContainer = Color(0xFF005146)
val md_theme_dark_onPrimaryContainer = Color(0xFF7FF8E6)
val md_theme_dark_secondary = Color(0xFFB0CCC6)
val md_theme_dark_onSecondary = Color(0xFF1C3531)
val md_theme_dark_secondaryContainer = Color(0xFF334B47)
val md_theme_dark_onSecondaryContainer = Color(0xFFCCE8E2)
val md_theme_dark_tertiary = Color(0xFFB0CBE3)
val md_theme_dark_onTertiary = Color(0xFF1B3447)
val md_theme_dark_tertiaryContainer = Color(0xFF324A60)
val md_theme_dark_onTertiaryContainer = Color(0xFFCCE7FF)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF0E1512)
val md_theme_dark_onBackground = Color(0xFFDEE5E1)
val md_theme_dark_surface = Color(0xFF0E1512)
val md_theme_dark_onSurface = Color(0xFFDEE5E1)
val md_theme_dark_surfaceVariant = Color(0xFF3F4946)
val md_theme_dark_onSurfaceVariant = Color(0xFFBEC9C5)
val md_theme_dark_outline = Color(0xFF889390)
val md_theme_dark_inverseOnSurface = Color(0xFF0E1512)
val md_theme_dark_inverseSurface = Color(0xFFDEE5E1)
val md_theme_dark_inversePrimary = Color(0xFF006B5C)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFF63DBCA)
val md_theme_dark_outlineVariant = Color(0xFF3F4946)
val md_theme_dark_scrim = Color(0xFF000000)
