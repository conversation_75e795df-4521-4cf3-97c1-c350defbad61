package com.zara.assistant.ui.viewmodel;

/**
 * UI state for the main screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b&\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u009b\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\f\u001a\u00020\u0007\u0012\b\b\u0002\u0010\r\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0007\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u0016J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0007H\u00c6\u0003J\t\u0010*\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010,\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\t\u0010-\u001a\u00020\u0007H\u00c6\u0003J\t\u0010.\u001a\u00020\u0005H\u00c6\u0003J\t\u0010/\u001a\u00020\u0007H\u00c6\u0003J\t\u00100\u001a\u00020\u0007H\u00c6\u0003J\t\u00101\u001a\u00020\nH\u00c6\u0003J\t\u00102\u001a\u00020\u0007H\u00c6\u0003J\t\u00103\u001a\u00020\u0007H\u00c6\u0003J\t\u00104\u001a\u00020\u0007H\u00c6\u0003J\u000f\u00105\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fH\u00c6\u0003J\u00a4\u0001\u00106\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00072\b\b\u0002\u0010\f\u001a\u00020\u00072\b\b\u0002\u0010\r\u001a\u00020\u00072\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00072\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u0007H\u00c6\u0001\u00a2\u0006\u0002\u00107J\u0013\u00108\u001a\u00020\u00072\b\u00109\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010:\u001a\u00020;H\u00d6\u0001J\t\u0010<\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0015\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0019R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0019R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0019R\u0015\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\u001a\u0010\u001bR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\r\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0011\u0010\f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0011\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0019R\u0011\u0010\u0011\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0018R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'\u00a8\u0006="}, d2 = {"Lcom/zara/assistant/ui/viewmodel/MainUiState;", "", "voiceState", "Lcom/zara/assistant/domain/model/VoiceState;", "voiceStateText", "", "isWakeWordActive", "", "isWakeWordEnabled", "wakeWordSensitivity", "", "isWakeWordServiceRunning", "shouldStartWakeWordService", "permissionsGranted", "missingPermissions", "", "showPermissionDialog", "showSettings", "errorMessage", "lastWakeWordDetection", "", "isFirstLaunch", "(Lcom/zara/assistant/domain/model/VoiceState;Ljava/lang/String;ZZFZZZLjava/util/List;ZZLjava/lang/String;Ljava/lang/Long;Z)V", "getErrorMessage", "()Ljava/lang/String;", "()Z", "getLastWakeWordDetection", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMissingPermissions", "()Ljava/util/List;", "getPermissionsGranted", "getShouldStartWakeWordService", "getShowPermissionDialog", "getShowSettings", "getVoiceState", "()Lcom/zara/assistant/domain/model/VoiceState;", "getVoiceStateText", "getWakeWordSensitivity", "()F", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Lcom/zara/assistant/domain/model/VoiceState;Ljava/lang/String;ZZFZZZLjava/util/List;ZZLjava/lang/String;Ljava/lang/Long;Z)Lcom/zara/assistant/ui/viewmodel/MainUiState;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class MainUiState {
    @org.jetbrains.annotations.NotNull()
    private final com.zara.assistant.domain.model.VoiceState voiceState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String voiceStateText = null;
    private final boolean isWakeWordActive = false;
    private final boolean isWakeWordEnabled = false;
    private final float wakeWordSensitivity = 0.0F;
    private final boolean isWakeWordServiceRunning = false;
    private final boolean shouldStartWakeWordService = false;
    private final boolean permissionsGranted = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> missingPermissions = null;
    private final boolean showPermissionDialog = false;
    private final boolean showSettings = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long lastWakeWordDetection = null;
    private final boolean isFirstLaunch = false;
    
    public MainUiState(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState voiceState, @org.jetbrains.annotations.NotNull()
    java.lang.String voiceStateText, boolean isWakeWordActive, boolean isWakeWordEnabled, float wakeWordSensitivity, boolean isWakeWordServiceRunning, boolean shouldStartWakeWordService, boolean permissionsGranted, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> missingPermissions, boolean showPermissionDialog, boolean showSettings, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastWakeWordDetection, boolean isFirstLaunch) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.VoiceState getVoiceState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVoiceStateText() {
        return null;
    }
    
    public final boolean isWakeWordActive() {
        return false;
    }
    
    public final boolean isWakeWordEnabled() {
        return false;
    }
    
    public final float getWakeWordSensitivity() {
        return 0.0F;
    }
    
    public final boolean isWakeWordServiceRunning() {
        return false;
    }
    
    public final boolean getShouldStartWakeWordService() {
        return false;
    }
    
    public final boolean getPermissionsGranted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMissingPermissions() {
        return null;
    }
    
    public final boolean getShowPermissionDialog() {
        return false;
    }
    
    public final boolean getShowSettings() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getLastWakeWordDetection() {
        return null;
    }
    
    public final boolean isFirstLaunch() {
        return false;
    }
    
    public MainUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.domain.model.VoiceState component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component13() {
        return null;
    }
    
    public final boolean component14() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.zara.assistant.ui.viewmodel.MainUiState copy(@org.jetbrains.annotations.NotNull()
    com.zara.assistant.domain.model.VoiceState voiceState, @org.jetbrains.annotations.NotNull()
    java.lang.String voiceStateText, boolean isWakeWordActive, boolean isWakeWordEnabled, float wakeWordSensitivity, boolean isWakeWordServiceRunning, boolean shouldStartWakeWordService, boolean permissionsGranted, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> missingPermissions, boolean showPermissionDialog, boolean showSettings, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastWakeWordDetection, boolean isFirstLaunch) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}