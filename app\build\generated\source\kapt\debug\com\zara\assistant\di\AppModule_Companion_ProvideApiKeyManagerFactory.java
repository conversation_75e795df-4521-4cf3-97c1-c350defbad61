// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.utils.ApiKeyManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_Companion_ProvideApiKeyManagerFactory implements Factory<ApiKeyManager> {
  private final Provider<Context> contextProvider;

  public AppModule_Companion_ProvideApiKeyManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ApiKeyManager get() {
    return provideApiKeyManager(contextProvider.get());
  }

  public static AppModule_Companion_ProvideApiKeyManagerFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_Companion_ProvideApiKeyManagerFactory(contextProvider);
  }

  public static ApiKeyManager provideApiKeyManager(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.Companion.provideApiKeyManager(context));
  }
}
