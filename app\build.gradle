plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.zara.assistant'
    compileSdk 34

    defaultConfig {
        applicationId "com.zara.assistant"
        minSdk 26
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        // API keys are loaded from assets/api_keys.key at runtime
        buildConfigField "String", "COHERE_API_KEY", "\"placeholder\""
        buildConfigField "String", "PERPLEXITY_API_KEY", "\"placeholder\""
        buildConfigField "String", "PORCUPINE_ACCESS_KEY", "\"placeholder\""

        // NDK configuration disabled - using Azure Speech Services instead
        // ndk {
        //     abiFilters 'arm64-v8a', 'armeabi-v7a'
        // }

        // externalNativeBuild {
        //     cmake {
        //         cppFlags '-std=c++17', '-frtti', '-fexceptions'
        //         arguments '-DANDROID_STL=c++_shared', '-DANDROID_PLATFORM=android-26'
        //     }
        // }
    }

    // External native build configuration disabled - using Azure Speech Services
    // externalNativeBuild {
    //     cmake {
    //         path "src/main/cpp/CMakeLists.txt"
    //         version "3.22.1"
    //     }
    // }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "DEBUG_MODE", "false"
        }
        debug {
            minifyEnabled false
            buildConfigField "boolean", "DEBUG_MODE", "true"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        compose true
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion compose_version
    }

    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    // Core Android
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    
    // Jetpack Compose BOM
    implementation platform('androidx.compose:compose-bom:2024.02.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material:material-icons-extended'
    implementation 'androidx.navigation:navigation-compose:2.7.4'
    
    // ViewModel and LiveData
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    
    // Dependency Injection - Hilt
    implementation "com.google.dagger:hilt-android:$hilt_version"
    kapt "com.google.dagger:hilt-compiler:$hilt_version"
    implementation "androidx.hilt:hilt-navigation-compose:1.1.0"
    implementation "androidx.hilt:hilt-work:1.1.0"
    kapt "androidx.hilt:hilt-compiler:1.1.0"
    
    // Room Database
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    kapt "androidx.room:room-compiler:$room_version"
    
    // Networking
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.squareup.okhttp3:okhttp:$okhttp_version"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttp_version"

    // TensorFlow Lite for on-device ML
    implementation 'org.tensorflow:tensorflow-lite:2.14.0'
    implementation('org.tensorflow:tensorflow-lite-support:0.4.4') {
        exclude group: 'org.tensorflow', module: 'tensorflow-lite-support-api'
    }
    implementation 'org.tensorflow:tensorflow-lite-metadata:0.4.4'
    
    // Coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_version"
    
    // WorkManager
    implementation "androidx.work:work-runtime-ktx:$work_version"

    // Microsoft Azure Speech Services SDK
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.43.0'
    
    // Picovoice Porcupine
    implementation 'ai.picovoice:porcupine-android:3.0.1'
    
    // Permissions
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'
    
    // JSON
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Preferences DataStore
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:$compose_version"
}
