// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.zara.assistant.di;

import android.content.Context;
import com.zara.assistant.data.local.database.ZaraDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideZaraDatabaseFactory implements Factory<ZaraDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideZaraDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ZaraDatabase get() {
    return provideZaraDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideZaraDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideZaraDatabaseFactory(contextProvider);
  }

  public static ZaraDatabase provideZaraDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideZaraDatabase(context));
  }
}
